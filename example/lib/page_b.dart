import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:test_page/page_a.dart';

class PageB extends StatelessWidget {
  const PageB({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    debugPrint('PageB build $args');
    return Scaffold(
      appBar: AppBar(
        title: Text('PageB'),
        actions: [
          TextButton(
              onPressed: () {
                Navigator.of(context).pop("ss b");
              },
              child: Text('back')),
        ],
      ),
      body: Column(
        children: [
          TextButton(
              onPressed: () {
                Navigator.of(context).push(PageRouteBuilder(
                    pageBuilder: (BuildContext context,
                        Animation<double> animation,
                        Animation<double> secondaryAnimation) {
                      return PageA();
                    },
                    settings: RouteSettings(name: "a", arguments: {"a": "a"})));
              },
              child: Text('jump a')),
          SizedBox(
            width: 100,
            height: 100,
            child: CachedNetworkImage(
                memCacheWidth: 100,
                memCacheHeight: 100,
                imageUrl:
                'https://avatars.githubusercontent.com/u/56351438?s=80&v=4'),
          ),
          SizedBox(
            width: 100,
            height: 100,
            child: CachedNetworkImage(
                memCacheWidth: 100,
                memCacheHeight: 100,
                imageUrl:
                'https://avatars.githubusercontent.com/u/74346222?s=80&v=4'),
          ),
        ],
      ),
    );
  }
}
