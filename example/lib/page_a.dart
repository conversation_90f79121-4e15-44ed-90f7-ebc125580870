import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:test_page/page_b.dart';

class PageA extends StatelessWidget {
  const PageA({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    debugPrint('PageA build $args');
    return Scaffold(
      appBar: AppBar(
        title: Text('PageA'),
        actions: [
          TextButton(onPressed: () {
            Navigator.of(context).pop("ss a");
          }, child: Text('back')),
        ],
      ),
      body: Column(
        children: [
          TextButton(onPressed: () {
            Navigator.of(context).push(PageRouteBuilder(
                pageBuilder: (BuildContext context,
                    Animation<double> animation,
                    Animation<double> secondaryAnimation) {
                  return PageB();
                }, settings:
            RouteSettings(name: "PageB", arguments: {"b": "b"})), );
          }, child: Text('jump b')),
          SizedBox(
            width: 100,
            height: 100,
            child: CachedNetworkImage(
                imageUrl:
                'https://avatars.githubusercontent.com/u/38371462?s=80&v=4'),
          ),
          SizedBox(
            width: 100,
            height: 100,
            child: CachedNetworkImage(
                imageUrl:
                'https://avatars.githubusercontent.com/u/105302020?s=80&v=4'),
          ),
        ],
      ),
    );
  }

}
