import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:test_page/page_a.dart';
import 'package:golden_eye/golden_eye.dart';
import 'package:golden_eye/custom_impl/test_tools_widgets_binding.dart';
import 'package:golden_eye/ui/root_widget.dart';
import 'package:golden_eye/kit/image_cache_info/core/custom_navigator_observer.dart';
import 'package:golden_eye/kit/move_record/move_record_kit.dart';

void main() {
  TestToolsWidgetsBinding();
  runApp(const ToolsRootWidget(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorObservers: [CustomNavigatorObserver.instance],
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
      builder: (context, child) {
        return FPSWidget(child: child ?? const SizedBox());
      },
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  // 1. 定义我们认可的、用于布局的 RenderObjectWidget 白名单
  static const Set<Type> _allowedRenderObjectWidgets = {
    // 布局类
    Scaffold,
    AppBar,
    Padding,
    Row,
    Column,
    Flex,
    Align,
    Center,
    SizedBox,
    ConstrainedBox,
    AspectRatio,
    Baseline,
    Stack,
    Positioned,

    // 装饰和效果类
    DecoratedBox,
    Opacity,
    Transform,
    ClipRect,
    ClipRRect,
    ClipOval,

    // 其他常用
    Image, // RawImage 是它的 RenderObjectWidget
    Text,
    CachedNetworkImage
  };

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((duration) {
      TestTools.show(Navigator.of(context).overlay);
      TestTools.checkFPS(Navigator.of(context).overlay);

      // 启用用户操作录制功能
      MoveRecordKit.startRecording(context: context);
      MoveRecordKit.enableVisualization(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      backgroundColor: Colors.grey,
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox(
              width: 100,
              height: 100,
              child: CachedNetworkImage(
                  imageUrl:
                      'https://res.halamate.com/o2/vip/246569604/e5/c6/1d/e426286817618bf6908d5491eae5c61d.gif.gif'),
            ),
            TextButton(
                onPressed: () {
                  TestTools.show(Navigator.of(context).overlay);
                },
                child: const Text('open')),
            TextButton(
                onPressed: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (BuildContext context) {
                            return const PageA();
                          },
                          settings: const RouteSettings(
                              name: "PageA", arguments: {"a": "a"})));
                },
                child: const Text('to PageA')),
          ],
        ),
      ),
    );
  }
}
