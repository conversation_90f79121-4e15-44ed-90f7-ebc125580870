import 'dart:developer';
import 'dart:isolate';
import 'package:vm_service/utils.dart';
import 'package:vm_service/vm_service.dart' as vm;
import 'package:vm_service/vm_service_io.dart';

/// 服务封装类，提供了与DartVM服务交互的方法
class ServiceWrapper {
  /// DartVM服务实例
  vm.VmService? _service;

  /// 当前IsolateID
  String? _isolateId;

  /// 获取当前IsolateID
  String? get isolateId {
    if (_isolateId != null) {
      return _isolateId;
    }
    _isolateId = Service.getIsolateID(Isolate.current);
    return _isolateId;
  }

  /// 获取VM服务实例
  /// 如果_service为空，则创建一个新的服务实例并赋值给_service
  Future<vm.VmService> getVMService() async {
    if (_service != null) return _service!;

    try {
      log('Getting VM service info...');
      ServiceProtocolInfo info = await Service.getInfo();
      log('VM service info: $info');
      String url = info.serverUri.toString();
      Uri uri = Uri.parse(url);
      Uri socketUri = convertToWebSocketUrl(serviceProtocolUrl: uri);
      log('Connecting to VM service at: $socketUri');
      _service = await vmServiceConnectUri(socketUri.toString());
      log('Connected to VM service.');
      return _service!;
    } catch (e, stacktrace) {
      log('Error getting VM service: $e\n$stacktrace');
      // Handle the error appropriately (e.g., show an error message, retry, etc.)
      rethrow; // Or return null, throw a custom exception, etc.
    }
  }

  /// 获取DartVM实例
  Future<vm.VM> getVM() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getVM();
  }

  /// 获取内存使用情况
  Future<vm.MemoryUsage> getMemoryUsage() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getMemoryUsage(isolateId!);
  }

  /// 获取类列表
  Future<vm.ClassList> getClassList() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getClassList(isolateId!);
  }

  /// 获取Isolate
  Future<vm.Isolate> getIsolate() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getIsolate(isolateId!);
  }

  /// 获取内存分配分析文件
  Future<vm.AllocationProfile> getAllocationProfile() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getAllocationProfile(isolateId!, reset: true);
  }

  /// 获取库列表
  Future<List<vm.LibraryRef>?> getLibraries() async {
    vm.Isolate isolate = await getIsolate();
    return isolate.libraries;
  }

  /// 获取快照
  Future<vm.HeapSnapshotGraph> getSnapshot() async {
    vm.VmService virtualMachine = await getVMService();
    vm.Isolate isolate = await getIsolate();
    return vm.HeapSnapshotGraph.getSnapshot(virtualMachine, isolate);
  }

  /// 获取实例列表
  Future<vm.InstanceSet> getInstances(String objectId, int limit) async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getInstances(isolateId!, objectId, limit);
  }

  /// 获取栈信息
  Future<vm.Stack> getStack() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getStack(isolateId!);
  }

  /// 获取对象信息
  Future<vm.Obj> getObject(String objectId, {int? offset, int? count}) async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getObject(isolateId!, objectId,
        offset: offset, count: count);
  }

  /// 获取入站引用
  Future<vm.InboundReferences> getInboundReferences(String objectId) async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getInboundReferences(isolateId!, objectId, 100);
  }

  /// 获取类堆栈统计
  Future<List<vm.ClassHeapStats>> getClassHeapStats() async {
    vm.AllocationProfile profile = await getAllocationProfile();
    List<vm.ClassHeapStats> list = profile.members!
        .where((element) =>
    element.bytesCurrent! > 0 || element.instancesCurrent! > 0)
        .toList();
    return list;
  }

  /// 获取脚本列表
  Future<vm.ScriptList> getScripts() async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.getScripts(isolateId!);
  }

  /// 执行表达式
  Future<vm.Response> evaluate(String targetId, String expression) async {
    vm.VmService virtualMachine = await getVMService();
    return virtualMachine.evaluate(isolateId!, targetId, expression);
  }

}