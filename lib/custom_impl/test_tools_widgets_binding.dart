
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

import '../kit/channel_monitor/core/custom_binary_messenger.dart';
import '../kit/image_cache_info/core/custom_image_cache.dart';

class TestToolsWidgetsBinding extends WidgetsFlutterBinding {

   static WidgetsBinding? ensureInitialized() {
      TestToolsWidgetsBinding();
      return WidgetsBinding.instance;
   }

   @override
   ImageCache createImageCache() => CustomImageCache.instance;

   @override
   BinaryMessenger createBinaryMessenger() => CustomBinaryMessenger.binaryMessenger;

}