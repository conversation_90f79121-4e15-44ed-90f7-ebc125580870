library golden_eye;

import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/data/req_model.dart';
import 'package:golden_eye/observer/observer.dart';
import 'data/statistic_model.dart';
import 'report/apm_reporter.dart';
import 'ui/float_btn.dart';
import 'ui/fps/fps_widget.dart';
export 'ui/fps/fps_widget.dart';

/// A Calculator.
class TestTools {
  TestTools._();

  static OverlayEntry? _entry;

  static TestToolsConfig? config;

  static AbsApmReporter? _report;
  static AbsApmReporter? get report => _report;

  static final routeObserver = TestToolNavigatorObserver();

  static bool isOpen() {
    return _entry != null;
  }

  static void setConfig(TestToolsConfig config) {
    TestTools.config = config;
  }

  static void show(OverlayState? overlayState) {
    hide();

    if (overlayState != null) {
      _entry = OverlayEntry(builder: (context) {
        return const FloatBtn();
      });
      overlayState.insert(_entry!);
    }
  }

  static void hide() {
    _entry?.remove();
    _entry = null;
  }

  static OverlayEntry? _fps;
  static void checkFPS(OverlayState? overlayState) {
    _fps?.remove();
    _fps = null;

    if (overlayState != null) {
      _fps = OverlayEntry(builder: (context) {
        return const FPSWidget(isReport: true, child: SizedBox.shrink());
      });
      overlayState.insert(_fps!);
    }
  }

  static void addHttpLog(HttpLogModel model) {
    dataCenter.addHttpLog(model);
  }

  static void addStatisticLog(StatisticModel model) {
    dataCenter.addStatisticLog(model);
  }

  static void setReport(AbsApmReporter report) {
    _report = report;
  }

}

class TestToolsConfig {
  Future<String> Function()? onGetDid;
  Future<Map> Function()? onGetBaseParams;
  Future<Map<String, dynamic>> Function()? onGetRemoteConfig;
}
