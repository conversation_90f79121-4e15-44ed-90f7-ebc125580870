
import 'package:flutter/material.dart';
import 'package:golden_eye/ui/launch/launch_observer.dart';

class TestToolNavigatorObserver extends NavigatorObserver {
  /// 需要监听路由变化数组
  final List<NavigatorObserver> _observers = [LaunchNavigatorObserver()];

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    for (final element in _observers) {
      element.didPush(route, previousRoute);
    }
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    for (final element in _observers) {
      element.didPop(route, previousRoute);
    }
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    for (final element in _observers) {
      element.didRemove(route, previousRoute);
    }
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    for (final element in _observers) {
      element.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    }
  }

  @override
  void didStartUserGesture(Route route, Route? previousRoute) {
    super.didStartUserGesture(route, previousRoute);
    for (final element in _observers) {
      element.didStartUserGesture(route, previousRoute);
    };
  }

  @override
  void didStopUserGesture() {
    super.didStopUserGesture();
    for (final element in _observers) {
      element.didStopUserGesture();
    }
  }
}