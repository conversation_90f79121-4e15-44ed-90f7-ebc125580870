import 'dart:math';

extension IntExtension on int {
  String prettySize([bool noSpace = false, bool one = false, int places = 1]) {
    var sizes = ['Bytes', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB'];

    var prettySize;

    for (int i = 0; i < sizes.length; ++i) {
      var s = pow(1024, i);
      var fixed;
      if (this >= s) {
        fixed = (this / s).toStringAsFixed(places);
        if (fixed.indexOf('.0') == fixed.length - 1 - places) {
          fixed = fixed.substring(0, fixed.length - 1 - places);
        }
        prettySize =
            fixed + (noSpace ? '' : ' ') + (one ? sizes[i][0] : sizes[i]);
      }
    }

    if (prettySize == null) {
      var unit = sizes[0];
      prettySize = '0${noSpace ? '' : ' '}${one ? unit[0] : unit}';
    }

    return prettySize;
  }
}
