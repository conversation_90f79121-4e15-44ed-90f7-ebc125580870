import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class ToolsWidget extends StatelessWidget {
  final void Function(ToolType)? onTap;
  final void Function()? onCancel;

  const ToolsWidget({super.key, this.onTap, this.onCancel});

  @override
  Widget build(BuildContext context) {
    const side = BorderSide(color: Colors.grey, width: 0.5);
    return Material(
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.zero,
        alignment: Alignment.topCenter,
        child: GridView(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            mainAxisSpacing: 0,
            crossAxisSpacing: 0,
          ),
          children: [
            _ToolsItem(ToolType.widgetInfo, onTap: onTap),
            _ToolsItem(ToolType.widgetDetail, onTap: onTap),
            _ToolsItem(ToolType.req, onTap: onTap),
            _ToolsItem(ToolType.statistic, onTap: onTap),
            _ToolsItem(ToolType.common, onTap: onTap),
            _ToolsItem(ToolType.fps, onTap: onTap),
            _ToolsItem(ToolType.checkImage,
                onTap: onTap, open: debugInvertOversizedImages),
            _ToolsItem(ToolType.highlightRepaints,
                onTap: onTap, open: debugRepaintRainbowEnabled),
            _ToolsItem(ToolType.highlightPointers,
                onTap: onTap, open: debugPaintPointersEnabled),
            _ToolsItem(ToolType.routeInfo, onTap: onTap),
            _ToolsItem(ToolType.pageLaunch, onTap: onTap),
            _ToolsItem(ToolType.imageCacheInfo, onTap: onTap),
            _ToolsItem(ToolType.channelMonitor, onTap: onTap),
            _ToolsItem(ToolType.memoryInfo, onTap: onTap),
            _ToolsItem(ToolType.showCode, onTap: onTap),
            _ToolsItem(ToolType.traffic, onTap: onTap),
            _ToolsItem(ToolType.alignment, onTap: onTap),
            _ToolsItem(ToolType.colorPicker, onTap: onTap),
            _ToolsItem(ToolType.remoteConfig, onTap: onTap),
          ],
        ),
      ),
    );
  }
}

enum ToolType {
  ///
  req,
  statistic,
  common,
  fps,
  checkImage,
  highlightRepaints,
  highlightPointers,
  routeInfo,
  pageLaunch,
  imageCacheInfo,
  channelMonitor,
  widgetInfo,
  widgetDetail,
  memoryInfo,
  showCode,
  /// 流量监控
  traffic,
  trafficList,
  alignment,
  colorPicker,
  remoteConfig,
}

extension ToolTypeExtension on ToolType {
  String get show {
    switch (this) {
      case ToolType.trafficList:
        return "Traffic list";
      case ToolType.req:
        return "Req";
      case ToolType.statistic:
        return "Statistic";
      case ToolType.common:
        return "Common";
      case ToolType.fps:
        return "FPS";
      case ToolType.checkImage:
        return "Oversize image";
      case ToolType.highlightRepaints:
        return "Highlight Repaints";
      case ToolType.highlightPointers:
        return "Highlight Pointers";
      case ToolType.routeInfo:
        return "Route Info";
      case ToolType.pageLaunch:
        return "Page Launch Time";
      case ToolType.imageCacheInfo:
        return "Image Cache Info";
      case ToolType.channelMonitor:
        return "Channel Monitor";
      case ToolType.widgetInfo:
        return "Widget Info";
      case ToolType.widgetDetail:
        return "Widget Detail";
      case ToolType.memoryInfo:
        return "Memory Info";
      case ToolType.showCode:
        return "Show Code";
      case ToolType.traffic:
        return "Traffic";
      case ToolType.alignment:
        return "Alignment";
      case ToolType.colorPicker:
        return "Color Picker";
      case ToolType.remoteConfig:
        return "Remote config";
    }
  }
}

class _ToolsItem extends StatelessWidget {
  final ToolType type;
  final bool open;

  const _ToolsItem(this.type, {this.onTap, this.open = false});

  final void Function(ToolType)? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap?.call(type),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration:
            BoxDecoration(border: Border.all(color: Colors.grey, width: 0.5)),
        alignment: Alignment.center,
        child: Text(
          type.show,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: open ? Colors.red : Colors.black,
            fontSize: 15,
          ),
        ),
      ),
    );
  }
}