import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/kit/alignment_tool/alignment_tool_page.dart';
import 'package:golden_eye/kit/color_picker/color_picker_page.dart';
import 'package:golden_eye/kit/image_cache_info/image_cache_info_kit.dart';
import 'package:golden_eye/kit/widget_info/widget_info_kit.dart';
import 'package:golden_eye/ui/draggable_widget.dart';
import 'package:golden_eye/ui/launch/launch_tool.dart';
import 'package:golden_eye/ui/tools_page.dart';

import '../kit/route_info/route_info_kit.dart';
import 'tools_widget.dart';

class FloatBtn extends StatefulWidget {
  const FloatBtn({super.key});

  @override
  State<StatefulWidget> createState() {
    return _FloatBtnState();
  }
}

class _FloatBtnState extends State<FloatBtn> {
  bool _showTool = false;

  OverlayEntry? _toolEntry;
  OverlayEntry? _pageEntry;

  @override
  Widget build(BuildContext context) {
    return DraggableWidget(
      width: 60,
      height: 60,
      child: GestureDetector(
        onTap: _onTap,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(50),
            border: Border.all(color: Colors.black38, width: 2),
          ),
          width: 60,
          height: 60,
          alignment: Alignment.center,
          child: Text(
            "FL",
            style: TextStyle(
                color: _showTool ? Colors.red : Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: 30),
          ),
        ),
      ),
    );
  }

  void _onTap() {
    final overlay = Navigator.of(context).overlay;
    if (overlay != null) {
      if (_showTool) {
        _toolEntry?.remove();
        _toolEntry = null;
        setState(() {
          _showTool = false;
        });
      } else {
        final entry = OverlayEntry(builder: (BuildContext context) {
          // ToolsWidget(onTap: _onMenuTap, onCancel: _onCancel)

          return Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: ToolsWidget(onTap: _onMenuTap, onCancel: _onCancel),
          );
        });
        overlay.insert(entry);
        _toolEntry = entry;
        setState(() {
          _showTool = true;
        });
      }
    }
  }

  void _onMenuTap(ToolType type) {
    debugPrint('menu -> $type');

    _onCancel();
    if (_menuTapInterrupt(type)) return;

    /// 打开新页面
    final overlay = Navigator.of(context).overlay;
    if (overlay != null) {
      final pageEntry = OverlayEntry(builder: (BuildContext context) {
        return Navigator(
          onGenerateRoute: (setting) {
            return PageRouteBuilder(pageBuilder: (_, __, ___) {
              return ToolsPage(type: type, closeCallback: _onClosePage);
            });
          },
        );
      });
      overlay.insert(pageEntry);
      _pageEntry = pageEntry;
    }
  }

  bool _menuTapInterrupt(ToolType type) {
    switch (type) {
      case ToolType.traffic:
        dataCenter.trafficToggle(context, onTap: ()=> _onMenuTap(ToolType.trafficList));
        return true;
      case ToolType.fps:
        dataCenter.fpsToggle();
        return true;
      case ToolType.checkImage:
        debugInvertOversizedImages = !debugInvertOversizedImages;
        return true;
      case ToolType.highlightRepaints:
        debugRepaintRainbowEnabled = !debugRepaintRainbowEnabled;
        return true;
      case ToolType.highlightPointers:
        debugPaintPointersEnabled = !debugPaintPointersEnabled;
        return true;
      case ToolType.routeInfo:
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const RouteInfoKit(),
                settings: const RouteSettings(
                    name: "RouteInfoPage", arguments: {"from": "FloatBtn"})));
        return true;
      case ToolType.pageLaunch:
        if (LaunchTool.enabled) {
          LaunchTool.closeCounter();
        } else {
          LaunchTool.openCounter(context);
        }
        return true;
      case ToolType.imageCacheInfo:
        if (ImageCacheInfoKit.enabled) {
          ImageCacheInfoKit.close();
        } else {
          ImageCacheInfoKit.open(context);
        }
        return true;
      case ToolType.widgetInfo:
        WidgetInfoKit.open(context);
        return true;
      case ToolType.colorPicker:
        ColorPickerTool.showColorPickerOverlay(context);
        return true;
        case ToolType.alignment:
          AlignmentTool.showAlignmentToolOverlay(context);
        return true;
      default:
        return false;
    }
  }

  void _onCancel() {
    debugPrint('cancel');
    _toolEntry?.remove();
    _toolEntry = null;
    setState(() {
      _showTool = false;
    });
  }

  void _onClosePage() {
    _pageEntry?.remove();
    _pageEntry = null;
  }
}
