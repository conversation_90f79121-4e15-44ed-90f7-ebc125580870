import 'package:flutter/material.dart';

class DraggableController {
  _DraggableWidgetState? _draggableState;
  Size? _size;

  void onAttach(_DraggableWidgetState state) {
    _draggableState = state;
  }

  void onUpdate() {
    _size = _draggableState?.updateSize();
  }

  void setSize(Size size) {
    _size = size;
  }

  Size curSize() {
    return _size ?? Size.zero;
  }
}

class DraggableWidget extends StatefulWidget {
  final DraggableController? controller;
  final double? offsetX;
  final double? offsetY;
  final Function(double dx, double dy)? onTouchUp;
  final Widget child;
  final double? width;
  final double? height;
  final double? maxY;

  /// 加到父容器的最大高度，默认全屏
  final double? totalHeight;

  /// 距离顶部间距
  final double? topGap;
  final double? defaultDx;

  /// 自动贴边
  final bool autoMooring;

  //
  final bool useGesture;
  final HitTestBehavior? gestureBehavior;

  final Function(Offset)? onDrag;
  final Function(Offset)? onUp;
  final Function(Offset, Size)? onGetSize;

  /// 是否使用Material包裹
  final bool useMaterial;

  const DraggableWidget(
      {super.key, required this.child,
      this.controller,
      this.width,
      this.height,
      this.offsetX,
      this.offsetY,
      this.onTouchUp,
      this.gestureBehavior,
      this.useGesture = false,
      this.autoMooring = true,
      this.useMaterial = true,
      this.onDrag,
      this.onUp,
      this.onGetSize,
      this.maxY,
      this.totalHeight,
      this.topGap = 50,
      this.defaultDx = 10});

  @override
  State<StatefulWidget> createState() => _DraggableWidgetState();
}

class _DraggableWidgetState extends State<DraggableWidget>
    with SingleTickerProviderStateMixin {
  /// 位置靠右
  bool right = true;
  double space = 0;
  double spaceY = 0;
  double startDx = 0;
  double startDy = 0;

  final double defaultDy = 1 - 180;

  late Offset offset;

  late Animation<double> _animation;
  late AnimationController _animationController;

  double _width = 0;
  double _height = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 200));
    _animation = Tween(begin: 0.0, end: 1.0).animate(_animationController)
      ..addListener(() {
        final dx = right
            ? startDx + _animation.value * space
            : startDx - _animation.value * space;
        final dy = startDy + _animation.value * spaceY;
        offset = Offset(dx, dy);
        if (mounted) setState(() => {});
      });
    widget.controller?.onAttach(this);
    _initData();
  }

  @override
  void didUpdateWidget(covariant DraggableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.width != widget.width || oldWidget.height != widget.height) {
      _initData();
    }
  }

  void _initData() {
    _width = widget.width ?? 0;
    _height = widget.height ?? 0;
    final left = widget.offsetX ?? (widget.defaultDx ?? 10.0);
    final top = widget.offsetY ?? defaultDy;
    offset = Offset(left, top);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      onPointerUp(const PointerUpEvent());
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _screenWidth = 375;
  double _screenHeight = 720;


  @override
  Widget build(BuildContext context) {

    final size = MediaQuery.of(context).size;
    _screenWidth = size.width;
    _screenHeight = size.height;

    var child = widget.useGesture
        ? GestureDetector(
            behavior: widget.gestureBehavior,
            onPanUpdate: onPanUpdate,
            onPanEnd: onPanEnd,
            child: widget.child,
          )
        : Listener(
            onPointerMove: onPointerMove,
            onPointerUp: onPointerUp,
            child: widget.child,
          );
    if (widget.useMaterial) {
      child = Material(color: Colors.transparent, child: child);
    }
    return Positioned(
        width: widget.width,
        height: widget.height,
        left: offset.dx,
        top: offset.dy,
        child: LayoutBuilder(builder: (_, __) {
          WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
            if (_width == 0 || _height == 0) {
              updateSize();
            }
          });
          return child;
        }));
  }

  void onPanUpdate(DragUpdateDetails event) {
    final double newOffsetX = offset.dx + event.delta.dx;
    final double newOffsetY = offset.dy + event.delta.dy;
    if (mounted) {
      setState(() {
        offset = Offset(newOffsetX, newOffsetY);
        widget.onDrag?.call(offset);
      });
    }
  }

  void onPanEnd(DragEndDetails event) {
    onPointerUp(const PointerUpEvent());
  }

  void onPointerMove(PointerMoveEvent pointerMoveEvent) {
    final double newOffsetX = offset.dx + pointerMoveEvent.delta.dx;
    final double newOffsetY = offset.dy + pointerMoveEvent.delta.dy;
    if (mounted) {
      setState(() {
        offset = Offset(newOffsetX, newOffsetY);
        widget.onDrag?.call(offset);
      });
    }
  }

  void onPointerUp(PointerUpEvent pointerUpEvent) {
    final topGap = widget.topGap ?? 50;
    if (offset.dy < 0) offset = offset.scale(1, 0);
    if (offset.dy < topGap) offset = Offset(offset.dx, topGap);
    final totalHeight = widget.totalHeight ?? _screenHeight;
    if (offset.dy > (totalHeight - _height - topGap)) {
      offset =
          offset.translate(0, totalHeight - _height - topGap - offset.dy);
    }
    double endDx = 0;
    double endDy = 0;
    if (widget.autoMooring) {
      if (offset.dx > (_screenWidth / 2 - _width / 2)) {
        right = true;
        endDx = _screenWidth - (widget.defaultDx ?? 10.0) - _width;
        space = endDx - offset.dx;
      } else {
        endDx = (widget.defaultDx ?? 10.0);
        space = offset.dx - endDx;
        right = false;
      }
    } else {
      space = 0;
      if (offset.dx + _width >= _screenWidth) {
        right = true;
        endDx = _screenWidth - _width;
        space = endDx - offset.dx;
      } else if (offset.dx < 0) {
        right = false;
        endDx = 0;
        space = offset.dx - endDx;
      } else {
        endDx = offset.dx;
      }

      spaceY = 0;
      final maxY = widget.maxY;
      if (maxY != null && offset.dy + _height > maxY) {
        spaceY = maxY - _height - offset.dy;
        endDy = maxY - _height;
      } else {
        endDy = offset.dy;
      }
    }
    startDx = offset.dx;
    startDy = offset.dy;

    widget.onUp?.call(Offset(endDx, endDy));

    if (mounted) {
      setState(() {});
      _animationController.forward(from: 0);
      widget.onTouchUp?.call(endDx, offset.dy);
    }
  }

  Size updateSize() {
    if (widget.child.key != null && widget.child.key is GlobalKey) {
      final key = widget.child.key as GlobalKey?;
      final Size? renderBox = key?.currentContext?.size;
      _width = renderBox?.width ?? 0;
      _height = renderBox?.height ?? 0;
      final size = Size(_width, _height);
      widget.onGetSize?.call(offset, size);
      widget.controller?.setSize(size);
      return size;
    }
    return Size.zero;
  }
}
