import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/data/req_model.dart';
import 'package:golden_eye/data/traffic_data.dart';
import 'package:golden_eye/ui/data_widget/http_detail_page.dart';
import 'package:golden_eye/ui/traffic/traffic_btn.dart';

import '../data_widget/common_widget.dart';

class TrafficListWidget extends StatefulWidget {
  const TrafficListWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return _TrafficListWidgetState();
  }
}

class _TrafficListWidgetState extends State<TrafficListWidget> {
  List<TrafficDetailItem>? _list;

  @override
  void initState() {
    super.initState();

    final map =
        Map<String, TrafficDetailItem>.from(dataCenter.trafficData.detailMap);
    final list = map.values.toList()
      ..sort(
              (a, b) => (b.rxBytes + b.txBytes).compareTo(a.rxBytes + a.txBytes));
    _list = list.length > 100 ? list.sublist(0, 100) : list;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemBuilder: (context, index) => _item(index),
      itemCount: _list?.length,
    );
  }

  Widget _item(int index) {
    const mb = 20*1024*1024;
    final item = _list?[index];
    final showIcon = (item?.txBytes ?? 0) > mb || (item?.rxBytes ?? 0) > mb;
    return GestureDetector(
      onTap: () async{
        if (item != null && item.url.isNotEmpty) {
          Clipboard.setData(ClipboardData(text: item.url));
        }
      },
      child: Column(
        children: [
          CommonWidget.text('Url', item?.url, showIcon: showIcon, isSuccess: !showIcon),
          CommonWidget.text('Tx', '${item?.txBytes.formatByte()}'),
          CommonWidget.text('Rx', '${item?.rxBytes.formatByte()}'),
          CommonWidget.text('Count', '${item?.count}'),
          Container(height: 1, width: double.infinity, color: Colors.white),
        ],
      ),
    );
  }
}
