import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/data/traffic_data.dart';

import '../draggable_widget.dart';

class TrafficBtn extends StatefulWidget {
  final VoidCallback? onTap;

  const TrafficBtn({super.key, this.onTap});

  @override
  State<StatefulWidget> createState() {
    return _TrafficBtnState();
  }

  static OverlayEntry? _entry;

  static void show(OverlayState? overlayState, {VoidCallback? onTap}) {
    hide();

    if (overlayState != null) {
      _entry = OverlayEntry(builder: (context) {
        return TrafficBtn(onTap: onTap);
      });
      overlayState.insert(_entry!);
    }
  }

  static void hide() {
    _entry?.remove();
    _entry = null;
  }
}

class _TrafficBtnState extends State<TrafficBtn> {
  double width = 100;
  double height = 100;

  Timer? _timer;

  /// 流量柱最大数值
  int _maxRx = 100;
  int _maxTx = 100;

  /// 上行
  List<int> _txList = [];

  /// 下行
  List<int> _rxList = [];

  @override
  void initState() {
    super.initState();
    dataCenter.trafficData.setWidgetWidth(width);
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _update();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }


  _update() {
    // final random = Random();
    // dataCenter.trafficData.addTraffic(
    //     rxTotal: random.nextInt(10000),
    //     txTotal: random.nextInt(10000),
    //     details: [
    //       TrafficDetailItem(
    //           rxBytes: random.nextInt(100000),
    //           txBytes: random.nextInt(100000),
    //           count: random.nextInt(4),
    //           url: 'dsasdas'),
    //     ]);
    _maxRx = 100;
    _maxTx = 100;
    _txList = List.from(dataCenter.trafficData.txList);
    _rxList = List.from(dataCenter.trafficData.rxList);
    for (var element in _txList) {
      if (element > _maxTx) {
        _maxTx = element;
      }
    }
    for (var element in _rxList) {
      if (element > _maxRx) {
        _maxRx = element;
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return DraggableWidget(
      width: width + 17,
      height: height * 2,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Column(
          children: [
            _buildTx(context),
            _buildRx(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTx(BuildContext context) {
    return Container(
      height: height,
      width: width + 17,
      padding: const EdgeInsets.all(6.0),
      decoration: BoxDecoration(
        color: const Color(0xaa000000),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (_txList.isNotEmpty)
            Text(
              'Tx: ${_txList.last.formatByte()}',
              textDirection: TextDirection.ltr,
              style: const TextStyle(color: Color(0xffffffff)),
            ),
          Text(
            'Total: ${dataCenter.trafficData.totalTx.formatByte()}',
            textDirection: TextDirection.ltr,
            style: const TextStyle(color: Color(0xffffffff)),
          ),
          const SizedBox(height: 4),
          Expanded(
            child: SizedBox(
              width: width,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  ..._txList.map((tx) {
                    final p = (tx / _maxTx).clamp(0.0, 1.0);

                    return Padding(
                      padding: const EdgeInsets.only(
                        right: 1.0,
                      ),
                      child: Container(
                        color: Color.lerp(
                          const Color(0xff4caf50),
                          _maxTx.isLarge ? const Color(0xfff44336) : const Color(0xff4caf50),
                          p,
                        ),
                        width: 4,
                        height: p * height,
                      ),
                    );
                  })
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRx(BuildContext context) {
    return Container(
      height: height,
      width: width + 17,
      padding: const EdgeInsets.all(6.0),
      decoration: BoxDecoration(
        color: const Color(0xaa000000),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (_rxList.isNotEmpty)
            Text(
              'Rx: ${_rxList.last.formatByte()}',
              textDirection: TextDirection.ltr,
              style: const TextStyle(color: Color(0xffffffff)),
            ),
          Text(
            'Total: ${dataCenter.trafficData.totalRx.formatByte()}',
            textDirection: TextDirection.ltr,
            style: const TextStyle(color: Color(0xffffffff)),
          ),
          const SizedBox(height: 4),
          Expanded(
            child: SizedBox(
              width: width,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  ..._rxList.map((tx) {
                    final p = (tx / _maxRx).clamp(0.0, 1.0);

                    return Padding(
                      padding: const EdgeInsets.only(
                        right: 1.0,
                      ),
                      child: Container(
                        color: Color.lerp(
                          const Color(0xff4caf50),
                          _maxRx.isLarge ? const Color(0xfff44336) : const Color(0xff4caf50),
                          p,
                        ),
                        width: 4,
                        height: p * height,
                      ),
                    );
                  })
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

extension ByteFormater on int {
  String formatByte() {
    if (this > 1024 * 1024 * 1024) {
      return "${(this / (1024 * 1024 * 1024)).toStringAsFixed(2)}GB";
    } else if (this > 1024 * 1024) {
      return "${(this / (1024 * 1024)).toStringAsFixed(2)}MB";
    } else if (this > 1024) {
      return "${(this / 1024).toStringAsFixed(2)}KB";
    }

    return "${this}B";
  }

  /// 大流量
  bool get isLarge => this > 1025*1024;
}
