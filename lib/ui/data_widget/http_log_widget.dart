import 'package:flutter/material.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/data/req_model.dart';
import 'package:golden_eye/ui/data_widget/http_detail_page.dart';

import 'common_widget.dart';

class HttpLogWidget extends StatefulWidget {
  const HttpLogWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return _HttpLogWidgetState();
  }
}

class _HttpLogWidgetState extends State<HttpLogWidget> {
  List<HttpLogModel>? _list;

  @override
  void initState() {
    super.initState();
    _list = dataCenter.getHttpLogs();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemBuilder: (context, index) => _item(index),
      itemCount: _list?.length,
    );
  }

  Widget _item(int index) {
    final item = _list?[index];
    return GestureDetector(
      onTap: () {
        if (item != null) {
          Navigator.of(context).push(PageRouteBuilder(pageBuilder:
              (BuildContext context, Animation<double> animation,
                  Animation<double> secondaryAnimation) {
            return HttpDetailPage(item: item);
          }));
        }
      },
      child: Column(
        children: [
          CommonWidget.text('Time', '${item?.time}'),
          CommonWidget.text('Url', item?.url),
          CommonWidget.text('Req', '${item?.req}'),
          CommonWidget.text('Code', '${item?.code}',
              showIcon: true, isSuccess: item?.code == 1),
          CommonWidget.text('Msg', '${item?.msg}'),
          CommonWidget.text('Data', '${item?.data}', maxLine: 2),
          Container(height: 1, width: double.infinity, color: Colors.white),
        ],
      ),
    );
  }
}
