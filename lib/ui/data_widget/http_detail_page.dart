import 'package:flutter/material.dart';
import 'package:golden_eye/data/req_model.dart';
import 'package:golden_eye/ui/ui_const.dart';

import 'common_widget.dart';

class HttpDetailPage extends StatelessWidget {
  final HttpLogModel item;
  const HttpDetailPage({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: colorPageBackground,
      appBar: AppBar(
        iconTheme: const IconThemeData(
          color: Colors.white,
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        title: const Text(
        'Req detail',
        style: TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            CommonWidget.text('Time', '${item.time}', fontSize: 25),
            const SizedBox(height: 10),
            CommonWidget.text('Url', item.url, fontSize: 25),
            const SizedBox(height: 10),
            CommonWidget.text('Req', '${item.req}', fontSize: 25),
            const SizedBox(height: 10),
            CommonWidget.text('Code', '${item.code}', fontSize: 25, showIcon: true, isSuccess: item.code == 1),
            const SizedBox(height: 10),
            CommonWidget.text('Msg', '${item.msg}', fontSize: 25),
            const SizedBox(height: 10),
            CommonWidget.text('Data', '${item.data}', fontSize: 25),
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

}