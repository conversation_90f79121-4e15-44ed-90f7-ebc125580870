import 'package:flutter/material.dart';
import 'package:golden_eye/golden_eye.dart';

class CommonWidget extends StatelessWidget {
  const CommonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 20),
        FutureBuilder<String>(
          future: TestTools.config?.onGetDid?.call(),
          builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
            return CommonWidget.text('Did', snapshot.data ?? '');
          },
        ),
        const SizedBox(height: 10),
        FutureBuilder<Map>(
          future: TestTools.config?.onGetBaseParams?.call(),
          builder: (BuildContext context, AsyncSnapshot<Map> snapshot) {
            return CommonWidget.text('Base params', '${snapshot.data ?? ''}');
          },
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  static Widget text(String title, String? text,
      {int? maxLine,
      double? fontSize,
      bool showIcon = false,
      bool isSuccess = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,

        children: [
          Text('$title: ',
              style: TextStyle(color: Colors.white, fontSize: fontSize ?? 15)),
          Visibility(
            visible: showIcon,
            child: Text(
              isSuccess ? '⭕️' : '❌',
              style: TextStyle(color: Colors.white, fontSize: fontSize ?? 15),
            ),
          ),
          Expanded(
            child: Text(
              text ?? '',
              style: TextStyle(color: Colors.white, fontSize: fontSize ?? 15),
              maxLines: maxLine,
            ),
          )
        ],
      ),
    );
  }
}
