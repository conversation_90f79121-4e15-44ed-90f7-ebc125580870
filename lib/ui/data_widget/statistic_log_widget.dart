import 'package:flutter/material.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/data/statistic_model.dart';

import 'common_widget.dart';

class StatisticLogWidget extends StatefulWidget {
  const StatisticLogWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return _StatisticLogWidgetState();
  }

}

class _StatisticLogWidgetState extends State<StatisticLogWidget> {
  List<StatisticModel>? _list;

  @override
  void initState() {
    super.initState();
    _list = dataCenter.getStatisticLogs();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemBuilder: (context, index) => _item(index),
      itemCount: _list?.length,
    );
  }

  Widget _item(int index) {
    final item = _list?[index];
    return Column(
      children: [
        CommonWidget.text('Time', '${item?.time}'),
        CommonWidget.text('Action', item?.action),
        CommonWidget.text('Params', '${item?.params}'),
        Container(height: 1, width: double.infinity, color: Colors.white),
      ],
    );
  }

}