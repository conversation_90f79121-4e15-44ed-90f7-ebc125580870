import 'package:flutter/material.dart';

import '../util/binding_ambiguate.dart';

const colorPageBackground = Color(0xbb000000);

const colorText333 = Color(0xFF333333);

const colorText666 = Color(0xFF666666);

const colorText999 = Color(0xFF999999);

const Size dotSize = Size(65.0, 65.0);

const double margin = 10.0;

const double bottomDistance = margin * 4;

const int kMaxTooltipLines = 10;

const double kScreenEdgeMargin = 10.0;

const double kTooltipPadding = 5.0;

const Color kTooltipBackgroundColor = Color.fromARGB(230, 60, 60, 60);

const Color kHighlightedRenderObjectFillColor =
Color.fromARGB(128, 128, 128, 255);

const Color kHighlightedRenderObjectBorderColor =
Color.fromARGB(128, 64, 64, 128);

const Color kTipTextColor = Color(0xFFFFFFFF);

final double ratio =
    bindingAmbiguate(WidgetsBinding.instance)!.window.devicePixelRatio;

final Size windowSize =
    bindingAmbiguate(WidgetsBinding.instance)!.window.physicalSize / ratio;

