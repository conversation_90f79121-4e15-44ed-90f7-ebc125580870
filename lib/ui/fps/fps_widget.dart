import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:golden_eye/data/data_center.dart';
import 'package:golden_eye/golden_eye.dart';

extension _FPS on Duration {
  double get fps => (1000 / inMilliseconds);
}

/// A widget that shows the current FPS.
class FPSWidget extends StatefulWidget {
  final Widget child;
  /// 是否上报
  final bool isReport;
  /// Where the [FPSWidget] should be positioned.
  final Alignment alignment;

  const FPSWidget({
    Key? key,
    required this.child,
    this.alignment = Alignment.topRight,
    this.isReport = false
  }) : super(key: key);

  @override
  State<FPSWidget> createState() => _FPSWidgetState();
}

class FPSController {
  void toggle() {
    _onToggle?.call();
  }

  VoidCallback? _onToggle;
}

class _FPSWidgetState extends State<FPSWidget> {
  Duration? prev;
  List<Duration> timings = [];
  double width = 150;
  double height = 100;
  late int framesToDisplay = width ~/ 5;
  final FPSController _controller = FPSController();

  bool _show = false;

  @override
  void initState() {
    super.initState();
    _controller._onToggle = _onToggle;
    dataCenter.addFpsController(_controller);
    if (widget.isReport) {
      SchedulerBinding.instance.addPostFrameCallback(update);
    }

  }

  @override
  void dispose() {
    dataCenter.removeFpsController(_controller);
    super.dispose();
  }

  void _onToggle() {
    if (!mounted) return;
    if (widget.isReport) return;
    if (_show) {
      _show = false;
    } else {
      _show = true;
      SchedulerBinding.instance.addPostFrameCallback(update);
    }
  }

  update(Duration duration) {
    if (prev != null) {
      final timing = duration - prev!;

      timings.add(timing);
      if (timings.length > framesToDisplay) {
        timings = timings.sublist(timings.length - framesToDisplay - 1);
      }

      if (widget.isReport) {
        /// 上报
        TestTools.report?.reportFps(timing.fps);
      }
    }
    prev = duration;

    if (mounted) {
      setState(() {
      });
    }


    if (mounted && (widget.isReport || _show)) {
      SchedulerBinding.instance.addPostFrameCallback(update);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isReport) {
      return const SizedBox.shrink();
    }
    return Material(
      child: Stack(
        alignment: widget.alignment,
        children: [
          widget.child,
          Visibility(
              visible: _show,
              child: IgnorePointer(
                ignoring: true,
                child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  height: height,
                  width: width + 17,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: const Color(0xaa000000),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      if (timings.isNotEmpty)
                        Text(
                          'FPS: ${timings.last.fps.toStringAsFixed(0)}',
                          textDirection: TextDirection.ltr,
                          style: const TextStyle(color: Color(0xffffffff)),
                        ),
                      const SizedBox(height: 4),
                      Expanded(
                        child: SizedBox(
                          width: width,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              ...timings.map((timing) {
                                final p = (timing.fps / 60).clamp(0.0, 1.0);

                                return Padding(
                                  padding: const EdgeInsets.only(
                                    right: 1.0,
                                  ),
                                  child: Container(
                                    color: Color.lerp(
                                      const Color(0xfff44336),
                                      const Color(0xff4caf50),
                                      p,
                                    ),
                                    width: 4,
                                    height: p * height,
                                  ),
                                );
                              })
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),)),
        ],
      ),
    );
  }
}
