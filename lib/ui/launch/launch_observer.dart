import 'package:flutter/material.dart';
import 'package:golden_eye/data/page_launch_model.dart';
import 'package:golden_eye/ui/launch/launch_tool.dart';

class LaunchNavigatorObserver extends NavigatorObserver {
  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);

    _startLog(route, previousRoute);
  }

  void _startLog(Route route, Route? previousRoute) {
    if (!LaunchTool.enabled) return;

    final before = DateTime.now().millisecondsSinceEpoch;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final now = DateTime.now().millisecondsSinceEpoch;
      LaunchTool.notifier.value = PageLaunchModel(
          costTime: now - before,
          previousPage: previousRoute?.settings.name,
          currentPage: route.settings.name);
    });
  }
}
