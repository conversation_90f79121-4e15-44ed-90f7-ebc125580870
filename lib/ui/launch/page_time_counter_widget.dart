import 'package:flutter/material.dart';
import 'package:golden_eye/data/page_launch_model.dart';
import 'package:golden_eye/ui/launch/launch_tool.dart';
import 'package:golden_eye/ui/ui_const.dart';

class PageTimeCounterWidget extends StatefulWidget {
  const PageTimeCounterWidget({super.key, required this.model});

  final PageLaunchModel model;

  @override
  State<PageTimeCounterWidget> createState() => _PageTimeCounterWidgetState();
}

class _PageTimeCounterWidgetState extends State<PageTimeCounterWidget> {
  /// 浮窗位置
  static var left = 0.0;
  static var top = 0.0;

  final TextStyle _textStyle = const TextStyle(
      fontSize: 11, color: colorText333, decoration: TextDecoration.none);

  @override
  Widget build(BuildContext context) {
    if (left == 0 && top == 0) {
      final size = MediaQuery.of(context).size;
      top = size.height * 0.5;
      left = size.width * 0.5;
    }

    return Positioned(
      top: top,
      left: left,
      child: Draggable(
        childWhenDragging: const SizedBox.shrink(),
        onDragEnd: (details) {
          if (mounted) {
            setState(() {
              left = details.offset.dx;
              top = details.offset.dy;
            });
          }
        },
        feedback: _counterWidget(),
        child: _counterWidget(),
      ),
    );
  }

  Widget _counterWidget() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFDDDDDD),
        borderRadius: BorderRadius.all(Radius.circular(4)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Column(
              children: [
                Text(
                  '${widget.model.previousPage} -> ${widget.model.currentPage}',
                  style: _textStyle,
                ),
                Text(
                  'Cost Time: ${widget.model.costTime} ms',
                  style: _textStyle,
                )
              ],
            ),
            GestureDetector(
              child: const Icon(
                Icons.close,
                color: Colors.white,
              ),
              onTap: () {
                LaunchTool.closeCounter();
              },
            )
          ],
        ),
      ),
    );
  }
}
