import 'package:flutter/material.dart';
import 'package:golden_eye/data/page_launch_model.dart';
import 'package:golden_eye/ui/launch/page_time_counter_widget.dart';

class LaunchTool {
  /// 是否开启页面启动耗时检测
  static bool enabled = false;

  /// 监听页面跳转变化
  static ValueNotifier<PageLaunchModel> notifier = ValueNotifier(
      PageLaunchModel(costTime: 0, previousPage: '', currentPage: ''));

  /// 监听回调
  static VoidCallback? callback;

  /// 页面耗时显示 widget
  static final OverlayEntry _overlayEntry = OverlayEntry(builder: (context) {
    return PageTimeCounterWidget(model: notifier.value);
  });

  /// 开始统计
  static void openCounter(BuildContext context) {
    enabled = true;
    callback = () {
      _overlayEntry.markNeedsBuild();
    };
    notifier.addListener(callback!);

    Navigator.of(context).overlay?.insert(_overlayEntry);
  }

  /// 关闭统计
  static void closeCounter() {
    enabled = false;
    if (callback != null) {
      notifier.removeListener(callback!);
    }
    _overlayEntry.remove();
  }
}
