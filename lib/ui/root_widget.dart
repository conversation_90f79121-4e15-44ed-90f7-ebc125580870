import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../util/binding_ambiguate.dart';
import '../kit/move_record/move_record_kit.dart';
import 'global.dart';

class ToolsRootWidget extends StatefulWidget {
  const ToolsRootWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  final Widget child;

  @override
  State<ToolsRootWidget> createState() => _ToolsRootWidgetState();
}

class _ToolsRootWidgetState extends State<ToolsRootWidget> {
  @override
  Widget build(BuildContext context) => Directionality(
      textDirection: TextDirection.ltr,
      child: Stack(
        children: [
          RepaintBoundary(
            key: root<PERSON>ey,
            child: MoveRecordKit.createRecordWidget(child: widget.child),
          ),
          MediaQuery(
              data: MediaQueryData.fromView(
                  bindingAmbiguate(WidgetsBinding.instance)!.window),
              child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
                child: Overlay(key: overlay<PERSON>ey),
              ))
        ],
      ));
}
