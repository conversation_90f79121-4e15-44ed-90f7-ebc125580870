import 'package:flutter/material.dart';
import 'package:golden_eye/kit/channel_monitor/channel_monitor_kit.dart';
import 'package:golden_eye/kit/remote_config/remote_config_page.dart';
import 'package:golden_eye/kit/show_code/show_code_kit.dart';
import 'package:golden_eye/kit/widget_info/widget_detail_kit.dart';
import 'package:golden_eye/ui/data_widget/common_widget.dart';
import 'package:golden_eye/ui/data_widget/http_log_widget.dart';
import 'package:golden_eye/ui/data_widget/statistic_log_widget.dart';
import 'package:golden_eye/ui/tools_widget.dart';
import 'package:golden_eye/ui/traffic/traffic_list_widget.dart';
import 'package:golden_eye/ui/ui_const.dart';

import '../kit/memory_info/memory_info_page.dart';

class ToolsPage extends StatelessWidget {
  final ToolType type;
  final VoidCallback? closeCallback;

  const ToolsPage({super.key, required this.type, this.closeCallback});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.transparent,
      backgroundColor: colorPageBackground,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.transparent,
        // backgroundColor: Colors.black38,
        title: Text(
          type.show,
          style: const TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          onPressed: () => closeCallback?.call(),
          icon: const Icon(
            Icons.close,
            color: Colors.white,
          ),
        ),
      ),
      body: Container(
        // color: Colors.black38,
        child: _body(),
      ),
    );
  }

  Widget _body() {
    switch (type) {
      case ToolType.req:
        return const HttpLogWidget();
      case ToolType.statistic:
        return const StatisticLogWidget();
      case ToolType.common:
        return const CommonWidget();
      case ToolType.widgetDetail:
        return const WidgetDetailKit();
      case ToolType.channelMonitor:
        return const ChannelMonitorKit();
      case ToolType.showCode:
        return const ShowCodeKit();
      case ToolType.memoryInfo:
        return const MemoryInfoKit();
      case ToolType.trafficList:
        return const TrafficListWidget();
      case ToolType.fps:
      case ToolType.checkImage:
      case ToolType.highlightRepaints:
      case ToolType.highlightPointers:
      case ToolType.routeInfo:
      case ToolType.pageLaunch:
      case ToolType.imageCacheInfo:
      case ToolType.colorPicker:
      case ToolType.alignment:
      case ToolType.widgetInfo:
      case ToolType.traffic:
        return const SizedBox.shrink();
      case ToolType.memoryInfo:
        return const MemoryInfoKit();
      case ToolType.remoteConfig:
        return const RemoteConfigPage();
    }
  }
}
