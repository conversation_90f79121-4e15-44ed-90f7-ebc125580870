import 'package:flutter/material.dart';
import 'package:golden_eye/kit/json_viewer/json_viewer_tool.dart';
import 'package:golden_eye/golden_eye.dart';

class RemoteConfigPage extends StatefulWidget {
  const RemoteConfigPage({super.key});

  @override
  State<RemoteConfigPage> createState() => _RemoteConfigPageState();
}

class _RemoteConfigPageState extends State<RemoteConfigPage> {
  Map<String, dynamic>? _json;

  @override
  void initState() {
    super.initState();

    _initData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_json == null) return const SizedBox.shrink();
    return Material(
      child: JsonViewerPage(json: _json),
    );
  }

  Future<void> _initData() async {
    final json = await TestTools.config?.onGetRemoteConfig?.call();
    if (mounted) {
      setState(() {
        _json = json;
      });
    }
  }
}

