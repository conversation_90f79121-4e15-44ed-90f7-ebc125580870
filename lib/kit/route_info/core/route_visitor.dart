
import 'package:flutter/widgets.dart';

import '../../../ui/global.dart';


class RouteVisitor {

  static RouteInfo? findRoute() {
    Element? topElement;
    var context = rootKey.currentContext;
    if (context == null) return null;
    final ModalRoute<dynamic>? rootRoute = ModalRoute.of(context);
    void listTopView(Element element) {
      if (element.widget is! PositionedDirectional) {
        if (element is RenderObjectElement &&
            element.renderObject is RenderBox) {
          final ModalRoute<dynamic>? route = ModalRoute.of(element);
          if (route != null && route != rootRoute) {
            topElement = element;
          }
        }
        element.visitChildren(listTopView);
      }
    }

    context.visitChildElements(listTopView);
    if (topElement != null) {
      final RouteInfo routeInfo = RouteInfo();
      routeInfo.current = ModalRoute.of(topElement!);
      buildNavigatorTree(topElement!, routeInfo);
      return routeInfo;
    }
    return null;
  }

  /// 反向遍历生成路由树
  static void buildNavigatorTree(Element element, RouteInfo routeInfo) {
    final NavigatorState? navigatorState =
    element.findAncestorStateOfType<NavigatorState>();

    if (navigatorState != null) {
      final RouteInfo parent = RouteInfo();
      parent.current = ModalRoute.of(navigatorState.context);
      routeInfo.parent = parent;
      routeInfo.parentNavigator = navigatorState.widget;
      return buildNavigatorTree(navigatorState.context as Element, parent);
    }
  }

}

class RouteInfo {
  ModalRoute<dynamic>? current;
  Widget? parentNavigator;
  RouteInfo? parent;
}