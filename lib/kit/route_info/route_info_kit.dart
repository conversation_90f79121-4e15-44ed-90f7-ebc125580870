import 'package:flutter/material.dart';
import 'package:golden_eye/kit/image_cache_info/core/custom_navigator_observer.dart';

import 'core/route_visitor.dart';

class RouteInfoKit extends StatefulWidget {

  const RouteInfoKit({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return RouteInfoPageState();
  }
}

class RouteInfoPageState extends State<RouteInfoKit> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Container(
            margin: const EdgeInsets.all(16),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 50),
                Container(
                    alignment: Alignment.topLeft,
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Text('Current Page Route Tree',
                            style: TextStyle(
                                color: Color(0xff333333),
                                fontSize: 16,
                                fontWeight: FontWeight.bold)),
                        Container(
                            margin: const EdgeInsets.only(top: 10),
                            alignment: Alignment.topLeft,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: buildRouteInfoWidget()))
                      ],
                    )),
              ],
            )),
      ),
    );
  }

  List<Widget> buildRouteInfoWidget() {
    final List<Widget> widgets = <Widget>[];
    List<Route> routes = CustomNavigatorObserver.instance.getRoutes();
    routes.removeLast();
    if (routes.isEmpty) {
      return widgets;
    }
    routes = routes.reversed.toList();
    for (int i = 0; i < routes.length; i++) {
      final route = routes[i];
      widgets.add(
        Container(
          padding: const EdgeInsets.all(12),
          width: MediaQuery.of(context).size.width,
          decoration: const BoxDecoration(
              color: Color(0xfff5f6f7),
              borderRadius: BorderRadius.all(Radius.circular(4.0))),
          alignment: Alignment.topLeft,
          child: RichText(
            text: TextSpan(
              children: <TextSpan>[
                const TextSpan(
                    text: 'Route Name: ',
                    style: TextStyle(
                        fontSize: 10,
                        color: Color(0xff333333),
                        height: 1.5,
                        fontWeight: FontWeight.bold)),
                TextSpan(
                    text: route.settings.name,
                    style: const TextStyle(
                        fontSize: 10, height: 1.5, color: Color(0xff666666))),
                const TextSpan(
                    text: '\nRoute Params: ',
                    style: TextStyle(
                        height: 1.5,
                        fontSize: 10,
                        color: Color(0xff333333),
                        fontWeight: FontWeight.bold)),
                TextSpan(
                    text: '${route.settings.arguments}',
                    style: const TextStyle(
                        fontSize: 10, height: 1.5, color: Color(0xff666666))),
                const TextSpan(
                    text: '\nAll info: ',
                    style: TextStyle(
                        fontSize: 10,
                        height: 1.5,
                        color: Color(0xff333333),
                        fontWeight: FontWeight.bold)),
                TextSpan(
                    text: route.toString(),
                    style: const TextStyle(
                        fontSize: 10, height: 1.5, color: Color(0xff666666))),
              ],
            ),
          ),
        ),
      );

      if (i < routes.length - 1) {
        widgets.add(
          Container(
            margin: const EdgeInsets.only(top: 10, bottom: 10),
            alignment: Alignment.center,
            child: const Icon(Icons.arrow_upward, size: 13, color: Colors.grey),
          ),
        );
      }
    }
    return widgets;
  }
}
