import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:golden_eye/kit/widget_info/widget_info_kit.dart';

import '../../../ui/global.dart';
import '../../../ui/ui_const.dart';
import '../../../util/binding_ambiguate.dart';
import 'hit_test.dart';
import 'inspector_overlay.dart';

class WidgetInfoInspector extends StatefulWidget {

  final bool? needDescription;

  final bool? needEdges;

  final Function(InspectorSelection selection)? onTap;

  const WidgetInfoInspector({Key? key, this.needDescription, this.needEdges, this.onTap}) : super(key: key);

  @override
  _WidgetInfoInspectorState createState() => _WidgetInfoInspectorState();

}

class _WidgetInfoInspectorState extends State<WidgetInfoInspector>
    with WidgetsBindingObserver {
  _WidgetInfoInspectorState()
      : selection = WidgetInspectorService.instance.selection;

  final window = bindingAmbiguate(WidgetsBinding.instance)!.window;

  Offset? _lastPointerLocation;
  OverlayEntry _overlayEntry = OverlayEntry(builder: (ctx) => Container());

  final InspectorSelection selection;

  void _inspectAt(Offset? position) {
    final List<RenderObject> selected =
        HitTest.hitTest(position, edgeHitMargin: 2.0);
    setState(() {
      selection.candidates = selected;
    });
  }

  void _handlePanDown(DragDownDetails event) {
    _lastPointerLocation = event.globalPosition;
    _inspectAt(event.globalPosition);
  }

  void _handlePanEnd(DragEndDetails details) {
    final Rect bounds =
        (Offset.zero & (window.physicalSize / window.devicePixelRatio))
            .deflate(1.0);
    if (!bounds.contains(_lastPointerLocation!)) {
      setState(() {
        selection.clear();
      });
    }
  }

  void _handleTap() {
    if (_lastPointerLocation != null) {
      _inspectAt(_lastPointerLocation);
    }
    widget.onTap?.call(selection);
  }

  @override
  void initState() {
    super.initState();
    selection.clear();
    bindingAmbiguate(WidgetsBinding.instance)
        ?.addPostFrameCallback((timeStamp) {
      _overlayEntry = OverlayEntry(builder: (_) => _DebugPaintButton(
        onPre: () {
          setState(() {
            int index = selection.index;
            if (index > 0) {
              selection.index = _findPreviousAvailableWidget(index);
            }
          });
        },
        onNext: () {
          setState(() {
            int index = selection.index;
            selection.index = _findNextAvailableWidget(index);
          });
        },
      ));
      overlayKey.currentState?.insert(_overlayEntry);
    });
  }

  int _findPreviousAvailableWidget(int currentIndex) {
    final currentCandidate = selection.candidates[currentIndex];
    for (int i = currentIndex - 1; i >= 0; i--) {
      RenderObject candidate = selection.candidates[i];
      if (candidate.paintBounds == currentCandidate.paintBounds) {
        continue;
      } else {
        return i;
      }
    }
    return currentIndex;
  }

  int _findNextAvailableWidget(int currentIndex) {
    final currentCandidate = selection.candidates[currentIndex];
    for (int i = currentIndex + 1; i < selection.candidates.length; i++) {
      RenderObject candidate = selection.candidates[i];
      if (candidate.paintBounds == currentCandidate.paintBounds) {
        continue;
      } else {
        return i;
      }
    }
    return currentIndex;
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = <Widget>[];
    GestureDetector gesture = GestureDetector(
      onTap: _handleTap,
      onPanDown: _handlePanDown,
      onPanEnd: _handlePanEnd,
      behavior: HitTestBehavior.opaque,
      child: IgnorePointer(
          child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height)),
    );
    children.add(gesture);
    children.add(InspectorOverlay(selection: selection, needDescription: widget.needDescription ?? true, needEdges: widget.needEdges ?? true));
    return Stack(children: children, textDirection: TextDirection.ltr);
  }

  @override
  void dispose() {
    super.dispose();
    if (_overlayEntry.mounted) {
      _overlayEntry.remove();
    }
  }
}

class _DebugPaintButton extends StatefulWidget {

  final VoidCallback? onPre;
  final VoidCallback? onNext;

  const _DebugPaintButton({Key? key, this.onPre, this.onNext}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _DebugPaintButtonState();
}

class _DebugPaintButtonState extends State<_DebugPaintButton> {
  double _dx = windowSize.width - dotSize.width - margin * 2;
  double _dy = windowSize.width - dotSize.width - bottomDistance;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _dx,
      top: _dy,
      child: Container(
        width: dotSize.width,
        height: dotSize.height * 3,
        child: GestureDetector(
          onPanUpdate: _buttonPanUpdate,
          child: Column(
            children: [
              FloatingActionButton(
                  onPressed: _showAllSize,
                  child: const Icon(Icons.close)),
              const SizedBox(height: 5),
              FloatingActionButton(
                  onPressed: widget.onPre,
                  child: const Icon(Icons.upload)),
              const SizedBox(height: 5),
              FloatingActionButton(
                  onPressed: widget.onNext,
                  child: const Icon(Icons.download)),
            ],
          ),
        ),
      ),
    );
  }

  void _buttonPanUpdate(DragUpdateDetails details) {
    setState(() {
      _dx = details.globalPosition.dx - dotSize.width / 2;
      _dy = details.globalPosition.dy - dotSize.width / 2;
    });
  }

  void _showAllSize() async {
    WidgetInfoKit.close();
  }

  @override
  void dispose() {
    super.dispose();
    debugPaintSizeEnabled = false;
    bindingAmbiguate(WidgetsBinding.instance)
        ?.addPostFrameCallback((timeStamp) {
      late RenderObjectVisitor visitor;
      visitor = (RenderObject child) {
        child.markNeedsPaint();
        child.visitChildren(visitor);
      };
      bindingAmbiguate(RendererBinding.instance)
          ?.renderView
          .visitChildren(visitor);
    });
  }
}
