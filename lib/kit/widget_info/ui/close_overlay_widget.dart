import 'package:flutter/material.dart';
import 'package:golden_eye/ui/draggable_widget.dart';

class CloseOverlayWidget extends StatelessWidget {
  final VoidCallback? onTap;
  const CloseOverlayWidget({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const Positioned.fill(child: SizedBox.shrink()),
        DraggableWidget(
          width: 60,
          height: 60,
          child: GestureDetector(
            onTap: () {
              onTap?.call();
            },
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(color: Colors.blueAccent, width: 1)
              ),
              child: const Icon(Icons.close),
            ),
          ),
        )
      ],
    );
  }
}
