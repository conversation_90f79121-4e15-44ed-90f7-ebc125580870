import 'package:flutter/material.dart';

import 'core/widget_info_inspector.dart';

class WidgetInfoKit {
  /// 是否开启图像缓存统计
  static bool enabled = false;

  static ValueNotifier<Map<String, int>> notifier = ValueNotifier({});

  /// 监听回调
  static VoidCallback? callback;

  /// 页面耗时显示 widget
  static final OverlayEntry _overlayEntry = OverlayEntry(builder: (context) {
    return const WidgetInfoInspector();
  });

  static void open(BuildContext context) {
    enabled = true;
    callback = () {
      _overlayEntry.markNeedsBuild();
    };
    notifier.addListener(callback!);

    Navigator.of(context).overlay?.insert(_overlayEntry);
  }

  static void close() {
    enabled = false;
    if (callback != null) {
      notifier.removeListener(callback!);
    }
    _overlayEntry.remove();
  }

}
