import 'package:flutter/material.dart';
import '../core/move_record_models.dart';

/// 用户操作录制可视化Overlay组件
class MoveRecordOverlay extends StatefulWidget {
  final Offset? currentTouchPosition;
  final List<ElementLayoutInfo> elementLayouts;

  const MoveRecordOverlay({
    Key? key,
    this.currentTouchPosition,
    required this.elementLayouts,
  }) : super(key: key);

  @override
  State<MoveRecordOverlay> createState() => _MoveRecordOverlayState();
}

class _MoveRecordOverlayState extends State<MoveRecordOverlay> {
  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: IgnorePointer(
        // 确保不拦截用户交互
        child: Container(
          // 明确设置为完全透明
          color: Colors.transparent,
          child: RepaintBoundary(
            // 使用RepaintBoundary优化重绘性能
            child: CustomPaint(
              // 设置size确保覆盖整个屏幕
              size: Size.infinite,
              painter: MoveRecordPainter(
                currentTouchPosition: widget.currentTouchPosition,
                elementLayouts: widget.elementLayouts,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void didUpdateWidget(MoveRecordOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当widget更新时，强制重绘
    if (oldWidget.elementLayouts != widget.elementLayouts ||
        oldWidget.currentTouchPosition != widget.currentTouchPosition) {
      // 触发重绘
      setState(() {});
    }
  }
}

/// 自定义绘制器，用于绘制触摸点和Element边界
class MoveRecordPainter extends CustomPainter {
  final Offset? currentTouchPosition;
  final List<ElementLayoutInfo> elementLayouts;
  final int layoutsHashCode; // 添加布局数据的哈希码用于比较

  MoveRecordPainter({
    this.currentTouchPosition,
    required this.elementLayouts,
  }) : layoutsHashCode = _calculateLayoutsHashCode(elementLayouts);

  /// 计算布局数据的哈希码
  static int _calculateLayoutsHashCode(List<ElementLayoutInfo> layouts) {
    if (layouts.isEmpty) return 0;

    int hash = layouts.length;
    for (final layout in layouts) {
      hash ^= layout.elementHashCode;
      hash ^= layout.globalPosition.hashCode;
      hash ^= layout.size.hashCode;
    }
    return hash;
  }

  @override
  void paint(Canvas canvas, Size size) {
    // 不需要清除画布，CustomPaint会自动处理
    // 直接绘制内容，保持底层透明

    // 绘制Element边界框（红色边框）
    _drawElementBounds(canvas);

    // 绘制当前触摸点（蓝色圆点）
    _drawCurrentTouchPoint(canvas);
  }

  /// 绘制Element边界框
  void _drawElementBounds(Canvas canvas) {
    final paint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    for (final element in elementLayouts) {
      // 过滤掉过小的元素，避免绘制过多无意义的边框
      if (element.size.width > 1.0 && element.size.height > 1.0) {
        final rect = Rect.fromLTWH(
          element.globalPosition.dx,
          element.globalPosition.dy,
          element.size.width,
          element.size.height,
        );
        canvas.drawRect(rect, paint);
      }
    }
  }

  /// 绘制当前触摸点
  void _drawCurrentTouchPoint(Canvas canvas) {
    if (currentTouchPosition != null) {
      final touchPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.fill;

      // 扩大触摸点半径到12.0像素
      canvas.drawCircle(currentTouchPosition!, 12.0, touchPaint);

      // 添加一个白色边框，使触摸点更明显
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      canvas.drawCircle(currentTouchPosition!, 12.0, borderPaint);
    }
  }

  @override
  bool shouldRepaint(covariant MoveRecordPainter oldDelegate) {
    // 比较触摸位置变化
    if (currentTouchPosition != oldDelegate.currentTouchPosition) {
      return true;
    }

    // 比较布局数据的哈希码，确保内容变化时能正确重绘
    if (layoutsHashCode != oldDelegate.layoutsHashCode) {
      return true;
    }

    // 比较布局数据的长度
    if (elementLayouts.length != oldDelegate.elementLayouts.length) {
      return true;
    }

    return false;
  }
}

/// 可视化验证控制器
class MoveRecordVisualizer {
  static OverlayEntry? _overlayEntry;
  static BuildContext? _overlayContext; // 保存context引用
  static Offset? _currentTouchPosition;
  static final List<ElementLayoutInfo> _elementLayouts = [];
  static int _overlayVersion = 0; // 版本号，用于强制重建
  static String? _currentRouteName; // 当前活跃页面的路由名称

  /// 显示可视化Overlay
  static void show(BuildContext context) {
    hide(); // 先隐藏之前的

    _overlayContext = context; // 保存context引用
    _overlayVersion++;
    _overlayEntry = OverlayEntry(
      builder: (context) => MoveRecordOverlay(
        key: ValueKey('overlay_$_overlayVersion'), // 使用版本号作为key强制重建
        currentTouchPosition: _currentTouchPosition,
        elementLayouts: List.from(_elementLayouts),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 隐藏可视化Overlay
  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _overlayContext = null;
    _currentTouchPosition = null;
    _currentRouteName = null;
  }

  /// 更新当前触摸位置
  static void updateTouchPosition(Offset? position) {
    _currentTouchPosition = position;
    _updateOverlay();
  }

  /// 清除触摸位置（用户抬起手指时调用）
  static void clearTouchPosition() {
    _currentTouchPosition = null;
    _updateOverlay();
  }

  /// 更新Element布局信息
  static void updateElementLayouts(List<ElementLayoutInfo> layouts, {String? routeName}) {
    // 检查是否是当前活跃页面的更新
    if (routeName != null && _currentRouteName != null && routeName != _currentRouteName) {
      // 如果不是当前活跃页面，忽略这次更新
      debugPrint('MoveRecordVisualizer: Ignoring layout update from inactive route: $routeName, current: $_currentRouteName');
      return;
    }

    // 更新当前路由名称
    if (routeName != null) {
      _currentRouteName = routeName;
    }

    // 先立即清除旧数据，确保不会有残留
    _elementLayouts.clear();
    _updateOverlay();

    // 如果是空布局（页面跳转时可能出现），直接返回
    if (layouts.isEmpty) {
      return;
    }

    // 添加新数据
    _elementLayouts.addAll(layouts);

    // 强制重建Overlay以确保清除旧的绘制内容
    _forceRebuildOverlay();
  }

  /// 清除所有数据
  static void clear() {
    _currentTouchPosition = null;
    _elementLayouts.clear();
    _currentRouteName = null;
    _forceRebuildOverlay();
  }

  /// 立即清除边界框显示（用于路由跳转时）
  static void clearElementBounds() {
    _elementLayouts.clear();
    _updateOverlay();
  }

  /// 设置当前活跃路由
  static void setCurrentRoute(String? routeName) {
    _currentRouteName = routeName;
    debugPrint('MoveRecordVisualizer: Current route set to: $routeName');
  }

  /// 更新Overlay显示
  static void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  /// 强制重建Overlay，确保完全清除旧内容
  static void _forceRebuildOverlay() {
    if (_overlayEntry != null && _overlayContext != null) {
      try {
        // 增加版本号
        _overlayVersion++;

        // 移除旧的Overlay
        _overlayEntry!.remove();
        _overlayEntry = null;

        // 立即创建新的Overlay，不等待帧回调
        _overlayEntry = OverlayEntry(
          builder: (context) => MoveRecordOverlay(
            key: ValueKey('overlay_$_overlayVersion'),
            currentTouchPosition: _currentTouchPosition,
            elementLayouts: List.from(_elementLayouts),
          ),
        );

        // 重新插入新的Overlay
        try {
          Overlay.of(_overlayContext!).insert(_overlayEntry!);
        } catch (e) {
          debugPrint('Failed to insert new overlay: $e');
          _overlayEntry = null;
        }
      } catch (e) {
        debugPrint('Failed to rebuild overlay: $e');
        // 如果重建失败，清除状态
        _overlayEntry = null;
        _overlayContext = null;
      }
    }
  }
}