import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../move_record_kit.dart';
import '../core/move_record_models.dart';

/// 用户操作录制页面
class MoveRecordPage extends StatefulWidget {
  const MoveRecordPage({Key? key}) : super(key: key);

  @override
  State<MoveRecordPage> createState() => _MoveRecordPageState();
}

class _MoveRecordPageState extends State<MoveRecordPage> {
  bool _isRecordingStarted = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('用户操作录制'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              MoveRecordKit.clearSession();
              setState(() {
                _isRecordingStarted = false;
              });
            },
            tooltip: '清除录制数据',
          ),
        ],
      ),
      body: ValueListenableBuilder<MoveRecordSession?>(
        valueListenable: MoveRecordKit.sessionNotifier,
        builder: (context, session, child) {
          return Column(
            children: [
              // 只在未开始录制时显示控制面板
              if (!_isRecordingStarted) _buildControlPanel(),
              if (!_isRecordingStarted) const Divider(),
              // 录制状态指示器
              if (_isRecordingStarted) _buildRecordingIndicator(),
              Expanded(
                child: session != null
                  ? _buildRecordingData(session)
                  : _buildEmptyState(),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    final stats = MoveRecordKit.getRecordingStats();
    final isRecording = stats['isRecording'] as bool;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  if (isRecording) {
                    MoveRecordKit.stopRecording();
                    setState(() {
                      _isRecordingStarted = false;
                    });
                  } else {
                    MoveRecordKit.startRecording(context: context);
                    setState(() {
                      _isRecordingStarted = true;
                    });
                    // 开始录制后自动返回上一页，让用户操作应用
                    Navigator.of(context).pop();
                  }
                },
                icon: Icon(isRecording ? Icons.stop : Icons.play_arrow),
                label: Text(isRecording ? '停止录制' : '开始录制'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isRecording ? Colors.red : Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () {
                  if (MoveRecordKit.visualizationEnabled) {
                    MoveRecordKit.disableVisualization();
                  } else {
                    MoveRecordKit.enableVisualization(context);
                  }
                  setState(() {});
                },
                icon: Icon(MoveRecordKit.visualizationEnabled
                  ? Icons.visibility_off
                  : Icons.visibility),
                label: Text(MoveRecordKit.visualizationEnabled
                  ? '关闭可视化'
                  : '开启可视化'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatsRow(stats),
        ],
      ),
    );
  }

  /// 构建统计信息行
  Widget _buildStatsRow(Map<String, dynamic> stats) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: [
        _buildStatChip('状态', stats['isRecording'] ? '录制中' : '已停止'),
        _buildStatChip('操作数', '${stats['actionsCount']}'),
        _buildStatChip('快照数', '${stats['snapshotsCount']}'),
        _buildStatChip('时长', '${(stats['duration'] / 1000).toStringAsFixed(1)}s'),
      ],
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(String label, String value) {
    return Chip(
      label: Text('$label: $value'),
      backgroundColor: Colors.grey[200],
    );
  }

  /// 构建录制状态指示器
  Widget _buildRecordingIndicator() {
    final stats = MoveRecordKit.getRecordingStats();
    final isRecording = stats['isRecording'] as bool;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: isRecording ? Colors.red.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
      child: Row(
        children: [
          Icon(
            isRecording ? Icons.fiber_manual_record : Icons.stop,
            color: isRecording ? Colors.red : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            isRecording ? '正在录制中...' : '录制已停止',
            style: TextStyle(
              color: isRecording ? Colors.red : Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: () {
              if (isRecording) {
                MoveRecordKit.stopRecording();
                setState(() {
                  _isRecordingStarted = false;
                });
              }
            },
            icon: const Icon(Icons.stop, size: 16),
            label: const Text('停止录制'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              minimumSize: const Size(80, 32),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.radio_button_unchecked, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('暂无录制数据', style: TextStyle(fontSize: 18, color: Colors.grey)),
          SizedBox(height: 8),
          Text('点击"开始录制"按钮开始记录用户操作', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  /// 构建录制数据显示
  Widget _buildRecordingData(MoveRecordSession session) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: '操作记录'),
              Tab(text: '布局快照'),
              Tab(text: 'JSON数据'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildActionsList(session.actions),
                _buildSnapshotsList(session.snapshots),
                _buildJsonView(session),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作记录列表
  Widget _buildActionsList(List<UserActionRecord> actions) {
    if (actions.isEmpty) {
      return const Center(child: Text('暂无操作记录'));
    }

    return ListView.builder(
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return ListTile(
          leading: _getActionIcon(action.type),
          title: Text(_getActionTitle(action.type)),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('时间: ${DateTime.fromMillisecondsSinceEpoch(action.timestamp)}'),
              if (action.position != null)
                Text('位置: (${action.position!.dx.toStringAsFixed(1)}, ${action.position!.dy.toStringAsFixed(1)})'),
              if (action.routeName != null)
                Text('页面: ${action.routeName}'),
            ],
          ),
        );
      },
    );
  }

  /// 构建布局快照列表
  Widget _buildSnapshotsList(List<PageLayoutSnapshot> snapshots) {
    if (snapshots.isEmpty) {
      return const Center(child: Text('暂无布局快照'));
    }

    return ListView.builder(
      itemCount: snapshots.length,
      itemBuilder: (context, index) {
        final snapshot = snapshots[index];
        return ExpansionTile(
          title: Text('快照 ${index + 1}'),
          subtitle: Text('${snapshot.elements.length} 个元素 - ${DateTime.fromMillisecondsSinceEpoch(snapshot.timestamp)}'),
          children: snapshot.elements.map((element) {
            return ListTile(
              dense: true,
              title: Text(element.widgetType),
              subtitle: Text('${element.globalPosition.dx.toStringAsFixed(1)}, ${element.globalPosition.dy.toStringAsFixed(1)} - ${element.size.width.toStringAsFixed(1)}x${element.size.height.toStringAsFixed(1)}'),
            );
          }).toList(),
        );
      },
    );
  }

  /// 构建JSON视图
  Widget _buildJsonView(MoveRecordSession session) {
    final jsonString = _formatJson(session.toJson());

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: jsonString));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('JSON数据已复制到剪贴板')),
                  );
                },
                icon: const Icon(Icons.copy),
                label: const Text('复制JSON'),
              ),
            ],
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: SelectableText(
              jsonString,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 获取操作类型图标
  Widget _getActionIcon(UserActionType type) {
    switch (type) {
      case UserActionType.layoutChange:
        return const Icon(Icons.view_quilt, color: Colors.orange);
      case UserActionType.touchDown:
        return const Icon(Icons.touch_app, color: Colors.blue);
      case UserActionType.touchMove:
        return const Icon(Icons.pan_tool, color: Colors.green);
    }
  }

  /// 获取操作类型标题
  String _getActionTitle(UserActionType type) {
    switch (type) {
      case UserActionType.layoutChange:
        return '布局变化';
      case UserActionType.touchDown:
        return '触摸按下';
      case UserActionType.touchMove:
        return '触摸滑动';
    }
  }

  /// 格式化JSON字符串
  String _formatJson(Map<String, dynamic> json) {
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(json);
  }
}