import 'package:flutter/material.dart';
import '../move_record_kit.dart';
import '../core/move_record_models.dart';

/// 录制状态悬浮指示器
class RecordingIndicator extends StatefulWidget {
  const RecordingIndicator({Key? key}) : super(key: key);

  @override
  State<RecordingIndicator> createState() => _RecordingIndicatorState();
}

class _RecordingIndicatorState extends State<RecordingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<MoveRecordSession?>(
      valueListenable: MoveRecordKit.sessionNotifier,
      builder: (context, session, child) {
        final stats = MoveRecordKit.getRecordingStats();
        final isRecording = stats['isRecording'] as bool;

        if (!isRecording) {
          return const SizedBox.shrink();
        }

        return Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          right: 16,
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Opacity(
                opacity: _animation.value,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 6),
                      const Text(
                        'REC',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

/// 录制指示器管理器
class RecordingIndicatorManager {
  static OverlayEntry? _indicatorEntry;

  /// 显示录制指示器
  static void show(BuildContext context) {
    hide(); // 先隐藏之前的

    _indicatorEntry = OverlayEntry(
      builder: (context) => const RecordingIndicator(),
    );

    Overlay.of(context).insert(_indicatorEntry!);
  }

  /// 隐藏录制指示器
  static void hide() {
    _indicatorEntry?.remove();
    _indicatorEntry = null;
  }
}