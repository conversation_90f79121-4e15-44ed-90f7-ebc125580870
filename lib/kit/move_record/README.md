# 用户操作录制功能 (Move Record)

## 功能概述

用户操作录制功能是一个轻量级的操作录制组件，能够记录用户界面操作和布局变化，用于问题排查时还原用户操作现场。

## 主要特性

1. **用户操作监听**：
   - 通过 `LayoutBuilder` 监听布局变化
   - 通过 `GestureDetector` 监听用户触摸操作（按下和滑动）

2. **页面信息获取**：
   - 使用 `CustomNavigatorObserver.instance.lastRouter` 获取当前页面Route
   - 获取完整的Element树布局信息

3. **布局信息记录**：
   - 遍历所有Element，获取对应的RenderObject
   - 记录每个RenderObject的尺寸和全局位置信息

4. **可视化验证**：
   - 蓝色圆点显示用户触摸位置和滑动轨迹
   - 红色边框显示所有Element的边界框
   - Overlay浮层覆盖在应用最上层，不影响正常交互

5. **数据管理**：
   - 紧凑的数据结构，适合网络传输
   - JSON格式导出，便于分析和存储

## 使用方法

### 1. 基本集成

用户操作录制功能已经集成到 `TestTools` 系统中，通过以下步骤使用：

1. 确保应用使用了 `ToolsRootWidget` 包装根组件
2. 在TestTools工具面板中找到 "Move Record" 选项
3. 点击进入用户操作录制页面

### 2. 录制操作

```dart
// 启用录制功能
MoveRecordKit.enabled = true;

// 开始录制
MoveRecordKit.startRecording();

// 停止录制
MoveRecordKit.stopRecording();

// 清除录制数据
MoveRecordKit.clearSession();
```

### 3. 可视化验证

```dart
// 开启可视化验证（显示触摸点和Element边界）
MoveRecordKit.enableVisualization(context);

// 关闭可视化验证
MoveRecordKit.disableVisualization();
```

### 4. 获取录制数据

```dart
// 获取当前录制会话
MoveRecordSession? session = MoveRecordKit.getCurrentSession();

// 获取JSON格式数据
Map<String, dynamic>? jsonData = MoveRecordKit.getSessionJson();

// 获取录制统计信息
Map<String, dynamic> stats = MoveRecordKit.getRecordingStats();
```

## 数据结构

### UserActionRecord (用户操作记录)
```dart
{
  "type": "touchDown|touchMove|layoutChange",
  "timestamp": 1640995200000,
  "position": {"dx": 100.0, "dy": 200.0}, // 仅触摸操作有效
  "routeName": "/home",
  "routeArguments": {"key": "value"}
}
```

### ElementLayoutInfo (Element布局信息)
```dart
{
  "elementType": "StatefulElement",
  "widgetType": "Container",
  "globalPosition": {"dx": 0.0, "dy": 0.0},
  "size": {"width": 100.0, "height": 50.0},
  "elementHashCode": 123456789,
  "widgetKey": "ValueKey('example')"
}
```

### PageLayoutSnapshot (页面布局快照)
```dart
{
  "timestamp": 1640995200000,
  "routeName": "/home",
  "routeArguments": {"key": "value"},
  "elements": [ElementLayoutInfo, ...]
}
```

### MoveRecordSession (完整录制会话)
```dart
{
  "sessionId": "session_1640995200000",
  "startTime": 1640995200000,
  "endTime": 1640995260000,
  "actions": [UserActionRecord, ...],
  "snapshots": [PageLayoutSnapshot, ...]
}
```

## 技术实现

### 核心组件

1. **MoveRecordWidget**: 监听组件，包装应用根组件
2. **LayoutCollector**: 布局信息收集器
3. **MoveRecordOverlay**: 可视化验证组件
4. **MoveRecordKit**: 管理类，提供API接口
5. **MoveRecordPage**: 录制数据展示页面

### 监听机制

#### **全面的布局变化监听**
- **WidgetsBindingObserver**: 监听应用生命周期和系统级变化
  - `didChangeMetrics()`: 监听屏幕尺寸、键盘弹出/收起
  - `didChangeAppLifecycleState()`: 监听应用前后台切换
- **持续帧回调**: 使用 `SchedulerBinding.addPostFrameCallback` 监听每一帧变化
  - 检测路由跳转（通过 `CustomNavigatorObserver`）
  - 实时监控页面状态变化
- **NotificationListener**: 监听各种布局通知
  - `SizeChangedLayoutNotification`: 尺寸变化通知
  - `LayoutChangedNotification`: 布局变化通知
  - `ScrollNotification`: 滚动相关通知
  - `OverscrollNotification`: 过度滚动通知
- **SizeChangedLayoutNotifier**: 专门监听尺寸变化
- **LayoutBuilder**: 监听约束变化
- **MediaQuery监听**: 检测键盘、屏幕旋转等变化

#### **触摸操作监听**
- **Listener**: 使用 `Listener` 替代 `GestureDetector`，确保在可滑动组件中准确监听
  - `onPointerDown`: 触摸按下
  - `onPointerMove`: 触摸移动
  - `onPointerUp`: 触摸抬起
  - `onPointerCancel`: 触摸取消

#### **防抖机制**
- 同一帧内的多次布局变化只触发一次处理
- 避免过度频繁的布局信息收集
- 保持应用性能不受影响

### 性能优化

- 限制触摸点记录数量（最多100个）
- 异常处理，避免单个Element错误影响整体遍历
- 可选的可视化功能，减少不必要的性能开销

## 注意事项

1. **内存管理**: 录制数据会占用内存，建议定期清理
2. **性能影响**: 开启可视化验证会有轻微性能影响
3. **数据量**: 复杂页面的布局快照可能包含大量Element信息
4. **隐私保护**: 录制数据可能包含敏感信息，请注意数据安全

## 故障排除

### 常见问题

1. **录制数据为空**: 检查 `MoveRecordKit.enabled` 是否为 `true`
2. **可视化不显示**: 确保调用了 `enableVisualization(context)`
3. **布局信息缺失**: 检查Element是否有对应的RenderObject

### 调试方法

```dart
// 查看录制状态
print(MoveRecordKit.getRecordingStats());

// 监听录制数据变化
MoveRecordKit.sessionNotifier.addListener(() {
  print('Recording data updated');
});
```