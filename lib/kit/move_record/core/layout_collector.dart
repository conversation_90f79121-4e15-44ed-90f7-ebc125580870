import 'package:flutter/material.dart';
import 'package:golden_eye/kit/image_cache_info/core/custom_navigator_observer.dart';
import 'move_record_models.dart';

/// 布局信息收集器
class LayoutCollector {
  /// 收集当前页面的布局信息
  static PageLayoutSnapshot? collectCurrentPageLayout() {
    try {
      final lastRoute = CustomNavigatorObserver.instance.lastRouter;
      if (lastRoute == null) return null;

      // 获取当前页面的根Element，而不是Navigator的context
      final currentPageElement = _getCurrentPageElement(lastRoute);
      if (currentPageElement == null) return null;

      final elements = <ElementLayoutInfo>[];
      _traverseElement(currentPageElement, elements);

      return PageLayoutSnapshot(
        timestamp: DateTime.now().millisecondsSinceEpoch,
        routeName: lastRoute.settings.name,
        routeArguments: lastRoute.settings.arguments as Map<String, dynamic>?,
        elements: elements,
      );
    } catch (e) {
      debugPrint('LayoutCollector.collectCurrentPageLayout error: $e');
      return null;
    }
  }

  /// 获取当前页面的根Element
  static Element? _getCurrentPageElement(Route route) {
    try {
      final navigatorContext = route.navigator?.context;
      if (navigatorContext == null) return null;

      // 查找Navigator中最顶层的可见页面Element
      Element? topPageElement;

      void findTopPageElement(Element element) {
        // 检查是否是页面相关的Widget
        final widget = element.widget;

        // 检查常见的页面Widget类型
        if (widget is Scaffold ||
            widget is Material ||
            widget.runtimeType.toString().contains('Page') ||
            widget.runtimeType.toString().contains('Scaffold')) {

          final renderObject = element.renderObject;
          if (renderObject != null && renderObject is RenderBox && renderObject.hasSize) {
            final size = renderObject.size;
            final position = renderObject.localToGlobal(Offset.zero);

            // 检查是否是全屏或接近全屏的页面
            if (size.width > 100 && size.height > 100 &&
                position.dx <= 50 && position.dy <= 200) { // 允许一些偏移（状态栏等）
              topPageElement = element;
            }
          }
        }

        // 继续遍历子元素
        element.visitChildren(findTopPageElement);
      }

      final navigatorElement = navigatorContext as Element;
      findTopPageElement(navigatorElement);

      // 如果没找到特定的页面Element，使用一个更通用的方法
      topPageElement ??= _findLargestVisibleElement(navigatorElement);

      return topPageElement;
    } catch (e) {
      debugPrint('_getCurrentPageElement error: $e');
      return null;
    }
  }

  /// 查找最大的可见Element（作为页面根Element的候选）
  static Element? _findLargestVisibleElement(Element rootElement) {
    Element? largestElement;
    double largestArea = 0;

    void findLargest(Element element) {
      final renderObject = element.renderObject;
      if (renderObject != null && renderObject is RenderBox && renderObject.hasSize) {
        final size = renderObject.size;
        final area = size.width * size.height;

        // 查找面积最大且位置合理的Element
        if (area > largestArea && area > 10000) { // 至少100x100的区域
          final position = renderObject.localToGlobal(Offset.zero);
          if (position.dx >= -50 && position.dy >= -50) { // 允许一些负偏移
            largestElement = element;
            largestArea = area;
          }
        }
      }

      element.visitChildren(findLargest);
    }

    findLargest(rootElement);
    return largestElement;
  }

  /// 遍历Element树，收集布局信息
  static void _traverseElement(Element element, List<ElementLayoutInfo> elements) {
    try {
      final renderObject = element.renderObject;
      if (renderObject != null && renderObject is RenderBox) {
        // 确保RenderBox已经完成布局
        if (renderObject.hasSize) {
          final size = renderObject.size;
          final globalPosition = _getGlobalPosition(renderObject);

          // 过滤掉过小的元素和无效位置
          if (globalPosition != null &&
              size.width > 1.0 &&
              size.height > 1.0 &&
              globalPosition.dx.isFinite &&
              globalPosition.dy.isFinite) {
            final layoutInfo = ElementLayoutInfo(
              elementType: element.runtimeType.toString(),
              widgetType: element.widget.runtimeType.toString(),
              globalPosition: globalPosition,
              size: size,
              elementHashCode: element.hashCode,
              widgetKey: element.widget.key?.toString(),
            );
            elements.add(layoutInfo);
          }
        }
      }

      // 递归遍历子元素
      element.visitChildren((child) {
        _traverseElement(child, elements);
      });
    } catch (e) {
      // 忽略单个元素的错误，继续遍历其他元素
      debugPrint('_traverseElement error for ${element.runtimeType}: $e');
    }
  }

  /// 获取RenderObject的全局位置
  static Offset? _getGlobalPosition(RenderBox renderBox) {
    try {
      return renderBox.localToGlobal(Offset.zero);
    } catch (e) {
      debugPrint('_getGlobalPosition error: $e');
      return null;
    }
  }
}