import 'package:flutter/material.dart';

/// 用户操作类型枚举
enum UserActionType {
  /// 布局变化
  layoutChange,
  /// 触摸按下
  touchDown,
  /// 触摸滑动
  touchMove,
}

/// 用户操作记录
class UserActionRecord {
  /// 操作类型
  final UserActionType type;

  /// 时间戳
  final int timestamp;

  /// 屏幕坐标（仅触摸操作有效）
  final Offset? position;

  /// 页面路由名称
  final String? routeName;

  /// 页面参数
  final Map<String, dynamic>? routeArguments;

  UserActionRecord({
    required this.type,
    required this.timestamp,
    this.position,
    this.routeName,
    this.routeArguments,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'timestamp': timestamp,
      'position': position != null ? {
        'dx': position!.dx,
        'dy': position!.dy,
      } : null,
      'routeName': routeName,
      'routeArguments': routeArguments,
    };
  }

  factory UserActionRecord.fromJson(Map<String, dynamic> json) {
    return UserActionRecord(
      type: UserActionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => UserActionType.layoutChange,
      ),
      timestamp: json['timestamp'] ?? 0,
      position: json['position'] != null
        ? Offset(json['position']['dx'], json['position']['dy'])
        : null,
      routeName: json['routeName'],
      routeArguments: json['routeArguments'],
    );
  }
}

/// Element布局信息记录
class ElementLayoutInfo {
  /// Element的运行时类型
  final String elementType;

  /// Widget的运行时类型
  final String widgetType;

  /// 全局位置
  final Offset globalPosition;

  /// 尺寸
  final Size size;

  /// Element的hashCode，用于标识
  final int elementHashCode;

  /// Widget的key（如果有）
  final String? widgetKey;

  ElementLayoutInfo({
    required this.elementType,
    required this.widgetType,
    required this.globalPosition,
    required this.size,
    required this.elementHashCode,
    this.widgetKey,
  });

  Map<String, dynamic> toJson() {
    return {
      'elementType': elementType,
      'widgetType': widgetType,
      'globalPosition': {
        'dx': globalPosition.dx,
        'dy': globalPosition.dy,
      },
      'size': {
        'width': size.width,
        'height': size.height,
      },
      'elementHashCode': elementHashCode,
      'widgetKey': widgetKey,
    };
  }

  factory ElementLayoutInfo.fromJson(Map<String, dynamic> json) {
    return ElementLayoutInfo(
      elementType: json['elementType'] ?? '',
      widgetType: json['widgetType'] ?? '',
      globalPosition: Offset(
        json['globalPosition']['dx'],
        json['globalPosition']['dy'],
      ),
      size: Size(
        json['size']['width'],
        json['size']['height'],
      ),
      elementHashCode: json['elementHashCode'] ?? 0,
      widgetKey: json['widgetKey'],
    );
  }
}

/// 页面布局快照
class PageLayoutSnapshot {
  /// 时间戳
  final int timestamp;

  /// 页面路由名称
  final String? routeName;

  /// 页面参数
  final Map<String, dynamic>? routeArguments;

  /// 所有Element的布局信息
  final List<ElementLayoutInfo> elements;

  PageLayoutSnapshot({
    required this.timestamp,
    this.routeName,
    this.routeArguments,
    required this.elements,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp,
      'routeName': routeName,
      'routeArguments': routeArguments,
      'elements': elements.map((e) => e.toJson()).toList(),
    };
  }

  factory PageLayoutSnapshot.fromJson(Map<String, dynamic> json) {
    return PageLayoutSnapshot(
      timestamp: json['timestamp'] ?? 0,
      routeName: json['routeName'],
      routeArguments: json['routeArguments'],
      elements: (json['elements'] as List?)
          ?.map((e) => ElementLayoutInfo.fromJson(e))
          .toList() ?? [],
    );
  }
}

/// 完整的录制会话数据
class MoveRecordSession {
  /// 会话ID
  final String sessionId;

  /// 开始时间
  final int startTime;

  /// 结束时间
  int? endTime;

  /// 用户操作记录列表
  final List<UserActionRecord> actions;

  /// 页面布局快照列表
  final List<PageLayoutSnapshot> snapshots;

  MoveRecordSession({
    required this.sessionId,
    required this.startTime,
    this.endTime,
    List<UserActionRecord>? actions,
    List<PageLayoutSnapshot>? snapshots,
  }) : actions = actions ?? [],
       snapshots = snapshots ?? [];

  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'startTime': startTime,
      'endTime': endTime,
      'actions': actions.map((a) => a.toJson()).toList(),
      'snapshots': snapshots.map((s) => s.toJson()).toList(),
    };
  }

  factory MoveRecordSession.fromJson(Map<String, dynamic> json) {
    return MoveRecordSession(
      sessionId: json['sessionId'] ?? '',
      startTime: json['startTime'] ?? 0,
      endTime: json['endTime'],
      actions: (json['actions'] as List?)
          ?.map((a) => UserActionRecord.fromJson(a))
          .toList(),
      snapshots: (json['snapshots'] as List?)
          ?.map((s) => PageLayoutSnapshot.fromJson(s))
          .toList(),
    );
  }
}