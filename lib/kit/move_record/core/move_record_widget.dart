import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:golden_eye/kit/image_cache_info/core/custom_navigator_observer.dart';
import 'layout_collector.dart';
import 'move_record_models.dart';
import '../ui/move_record_overlay.dart';
import '../move_record_kit.dart';

/// 用户操作录制监听组件
class MoveRecordWidget extends StatefulWidget {
  /// 需要监听的子Widget
  final Widget child;
  final Function(UserActionRecord)? onUserAction;
  final Function(PageLayoutSnapshot)? onLayoutSnapshot;

  const MoveRecordWidget({
    Key? key,
    required this.child,
    this.onUserAction,
    this.onLayoutSnapshot,
  }) : super(key: key);

  @override
  State<MoveRecordWidget> createState() => _MoveRecordWidgetState();
}

class _MoveRecordWidgetState extends State<MoveRecordWidget>
    with WidgetsBindingObserver {

  // 防抖机制相关
  int? _lastLayoutChangeFrame;
  bool _isLayoutChangeScheduled = false;

  // 持续帧回调标记
  bool _frameCallbackRegistered = false;

  // 上次记录的MediaQuery数据，用于检测键盘和屏幕变化
  MediaQueryData? _lastMediaQueryData;

  // 上次记录的路由信息，用于检测页面跳转
  String? _lastRouteName;

  @override
  void initState() {
    super.initState();

    // 注册应用生命周期观察者
    WidgetsBinding.instance.addObserver(this);

    // 注册持续帧回调，监听每一帧的变化
    _frameCallbackRegistered = true;
    _scheduleFrameCallback();

    // 初始布局变化检测
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scheduleLayoutChange('initial');
    });
  }

  @override
  void dispose() {
    // 移除观察者和回调
    WidgetsBinding.instance.removeObserver(this);
    _frameCallbackRegistered = false;
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    // 监听屏幕尺寸、键盘等变化
    super.didChangeMetrics();
    _scheduleLayoutChange('didChangeMetrics');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 监听应用生命周期变化
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _scheduleLayoutChange('appResumed');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 依赖变化时也检查路由变化（这通常在页面切换时发生）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkRouteChange('didChangeDependencies');
    });
  }

  /// 持续帧回调，监听每一帧的变化
  void _onFrame(Duration timeStamp) {
    if (!mounted) return;

    // 检测路由变化
    _checkRouteChange('frameCallback');
  }

  @override
  Widget build(BuildContext context) {
    // 检测MediaQuery变化（键盘、屏幕旋转等）
    final currentMediaQuery = MediaQuery.of(context);
    if (_lastMediaQueryData != currentMediaQuery) {
      _lastMediaQueryData = currentMediaQuery;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scheduleLayoutChange('mediaQueryChanged');
      });
    }

    // 在每次build时也检查路由变化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkRouteChange('build');
    });

    return NotificationListener<Notification>(
      // 监听所有通知，包括布局相关的通知
      onNotification: (notification) {
        _handleNotification(notification);
        return false; // 不阻止通知继续传播
      },
      child: SizeChangedLayoutNotifier(
        child: NotificationListener<SizeChangedLayoutNotification>(
          onNotification: (notification) {
            _scheduleLayoutChange('sizeChanged');
            return false;
          },
          child: LayoutBuilder(
            builder: (context, constraints) {
              // 检测LayoutBuilder约束变化
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _scheduleLayoutChange('layoutBuilder');
              });

              return Listener(
                // 使用Listener而不是GestureDetector，确保在可滑动组件中也能准确监听
                onPointerDown: (event) {
                  _onTouchDown(event.position);
                },
                onPointerMove: (event) {
                  _onTouchMove(event.position);
                },
                onPointerUp: (event) {
                  _onTouchUp();
                },
                onPointerCancel: (event) {
                  _onTouchUp();
                },
                child: widget.child,
              );
            },
          ),
        ),
      ),
    );
  }

  /// 调度帧回调
  void _scheduleFrameCallback() {
    if (_frameCallbackRegistered && mounted) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _onFrame(SchedulerBinding.instance.currentFrameTimeStamp);
        if (_frameCallbackRegistered && mounted) {
          _scheduleFrameCallback(); // 继续注册下一帧
        }
      });
    }
  }

  /// 调度布局变化检测（防抖机制）
  void _scheduleLayoutChange(String reason) {
    if (!mounted) return;

    final currentFrame = SchedulerBinding.instance.currentFrameTimeStamp.inMicroseconds;

    // 防抖：如果在同一帧内已经调度过，则跳过
    if (_lastLayoutChangeFrame == currentFrame) {
      return;
    }

    _lastLayoutChangeFrame = currentFrame;

    if (!_isLayoutChangeScheduled) {
      _isLayoutChangeScheduled = true;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _isLayoutChangeScheduled = false;
          _onLayoutChanged();
        }
      });
    }
  }

  /// 处理通知
  void _handleNotification(Notification notification) {
    // 监听各种布局相关的通知
    if (notification is SizeChangedLayoutNotification) {
      _scheduleLayoutChange('sizeChangedNotification');
    } else if (notification is LayoutChangedNotification) {
      _scheduleLayoutChange('layoutChangedNotification');
    } else if (notification is ScrollNotification) {
      // 滚动时也可能影响布局
      _scheduleLayoutChange('scrollNotification');
    } else if (notification is OverscrollNotification) {
      _scheduleLayoutChange('overscrollNotification');
    }
  }

  /// 检查路由变化
  void _checkRouteChange(String source) {
    try {
      final currentRoute = CustomNavigatorObserver.instance.lastRouter;
      final currentRouteName = currentRoute?.settings.name;
      if (_lastRouteName != currentRouteName) {
        debugPrint('MoveRecordWidget: Route change detected from $source: $_lastRouteName -> $currentRouteName');
        _lastRouteName = currentRouteName;
        _onRouteChanged();
        _scheduleLayoutChange('routeChanged_$source');
      }
    } catch (e) {
      debugPrint('MoveRecordWidget._checkRouteChange error: $e');
    }
  }

  /// 处理路由变化
  void _onRouteChanged() {
    try {
      // 获取当前路由名称
      final currentRoute = CustomNavigatorObserver.instance.lastRouter;
      final currentRouteName = currentRoute?.settings.name;

      // 调用路由变化处理，立即清除旧边界框
      MoveRecordKit.onRouteChanged(currentRouteName);
    } catch (e) {
      debugPrint('MoveRecordWidget._onRouteChanged error: $e');
    }
  }

  /// 处理布局变化
  void _onLayoutChanged() {
    try {
      final snapshot = LayoutCollector.collectCurrentPageLayout();
      if (snapshot != null) {
        widget.onLayoutSnapshot?.call(snapshot);

        // 同时记录布局变化操作
        final lastRoute = CustomNavigatorObserver.instance.lastRouter;
        final actionRecord = UserActionRecord(
          type: UserActionType.layoutChange,
          timestamp: DateTime.now().millisecondsSinceEpoch,
          routeName: lastRoute?.settings.name,
          routeArguments:
              lastRoute?.settings.arguments as Map<String, dynamic>?,
        );
        widget.onUserAction?.call(actionRecord);
      }
    } catch (e) {
      debugPrint('MoveRecordWidget._onLayoutChanged error: $e');
    }
  }

  /// 处理触摸按下
  void _onTouchDown(Offset position) {
    try {
      final lastRoute = CustomNavigatorObserver.instance.lastRouter;
      final actionRecord = UserActionRecord(
        type: UserActionType.touchDown,
        timestamp: DateTime.now().millisecondsSinceEpoch,
        position: position,
        routeName: lastRoute?.settings.name,
        routeArguments: lastRoute?.settings.arguments as Map<String, dynamic>?,
      );
      widget.onUserAction?.call(actionRecord);
    } catch (e) {
      debugPrint('MoveRecordWidget._onTouchDown error: $e');
    }
  }

  /// 处理触摸滑动
  void _onTouchMove(Offset position) {
    try {
      final lastRoute = CustomNavigatorObserver.instance.lastRouter;
      final actionRecord = UserActionRecord(
        type: UserActionType.touchMove,
        timestamp: DateTime.now().millisecondsSinceEpoch,
        position: position,
        routeName: lastRoute?.settings.name,
        routeArguments: lastRoute?.settings.arguments as Map<String, dynamic>?,
      );
      widget.onUserAction?.call(actionRecord);
    } catch (e) {
      debugPrint('MoveRecordWidget._onTouchMove error: $e');
    }
  }

  /// 处理触摸抬起
  void _onTouchUp() {
    try {
      // 通知可视化器清除触摸位置
      MoveRecordVisualizer.clearTouchPosition();
    } catch (e) {
      debugPrint('MoveRecordWidget._onTouchUp error: $e');
    }
  }
}
