import 'package:flutter/material.dart';
import 'core/move_record_models.dart';
import 'core/move_record_widget.dart';
import 'ui/move_record_overlay.dart';
import 'ui/recording_indicator.dart';

/// 用户操作录制工具包
class MoveRecordKit {
  /// 是否启用录制功能
  static bool enabled = true;

  /// 是否显示可视化验证
  static bool visualizationEnabled = true;

  /// 当前录制会话
  static MoveRecordSession? _currentSession;

  /// 录制数据变化通知器
  static ValueNotifier<MoveRecordSession?> sessionNotifier = ValueNotifier(null);

  /// 开始录制
  static void startRecording({String? sessionId, BuildContext? context}) {
    if (enabled && _currentSession == null) {
      _currentSession = MoveRecordSession(
        sessionId: sessionId ?? _generateSessionId(),
        startTime: DateTime.now().millisecondsSinceEpoch,
      );
      sessionNotifier.value = _currentSession;

      // 显示录制指示器
      if (context != null) {
        RecordingIndicatorManager.show(context);
      }

      debugPrint('MoveRecordKit: Recording started with session ${_currentSession!.sessionId}');
    }
  }

  /// 停止录制
  static void stopRecording() {
    if (_currentSession != null) {
      _currentSession!.endTime = DateTime.now().millisecondsSinceEpoch;
      debugPrint('MoveRecordKit: Recording stopped for session ${_currentSession!.sessionId}');
      sessionNotifier.value = _currentSession;

      // 隐藏录制指示器
      RecordingIndicatorManager.hide();
    }
  }

  /// 清除当前录制会话
  static void clearSession() {
    _currentSession = null;
    sessionNotifier.value = null;
    MoveRecordVisualizer.clear();
    RecordingIndicatorManager.hide();
    debugPrint('MoveRecordKit: Session cleared');
  }

  /// 获取当前录制会话
  static MoveRecordSession? getCurrentSession() {
    return _currentSession;
  }

  /// 获取录制数据的JSON格式
  static Map<String, dynamic>? getSessionJson() {
    return _currentSession?.toJson();
  }

  /// 开启可视化验证
  static void enableVisualization(BuildContext context) {
    visualizationEnabled = true;
    MoveRecordVisualizer.show(context);
    debugPrint('MoveRecordKit: Visualization enabled');
  }

  /// 关闭可视化验证
  static void disableVisualization() {
    visualizationEnabled = false;
    MoveRecordVisualizer.hide();
    debugPrint('MoveRecordKit: Visualization disabled');
  }

  /// 创建录制Widget
  static Widget createRecordWidget({
    required Widget child,
  }) {
    return MoveRecordWidget(
      onUserAction: _onUserAction,
      onLayoutSnapshot: _onLayoutSnapshot,
      child: child,
    );
  }

  /// 处理用户操作记录
  static void _onUserAction(UserActionRecord action) {
    if (_currentSession != null) {
      _currentSession!.actions.add(action);
      sessionNotifier.value = _currentSession;

      // 如果启用可视化，更新触摸位置
      if (visualizationEnabled) {
        if (action.type == UserActionType.touchDown || action.type == UserActionType.touchMove) {
          MoveRecordVisualizer.updateTouchPosition(action.position);
        }
      }
    }
  }

  /// 处理布局快照
  static void _onLayoutSnapshot(PageLayoutSnapshot snapshot) {
    if (_currentSession != null) {
      _currentSession!.snapshots.add(snapshot);
      sessionNotifier.value = _currentSession;
    }

    // 如果启用可视化，更新Element布局信息
    if (visualizationEnabled) {
      MoveRecordVisualizer.updateElementLayouts(
        snapshot.elements,
        routeName: snapshot.routeName,
      );
    }
  }

  /// 处理路由变化（立即清除旧边界框）
  static void onRouteChanged(String? newRouteName) {
    if (visualizationEnabled) {
      // 强制清除所有边界框
      MoveRecordVisualizer.clear();

      // 设置新的当前路由
      MoveRecordVisualizer.setCurrentRoute(newRouteName);

      debugPrint('MoveRecordKit: Route changed to: $newRouteName, cleared all boundaries');
    }
  }

  /// 生成会话ID
  static String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// 获取录制统计信息
  static Map<String, dynamic> getRecordingStats() {
    if (_currentSession == null) {
      return {
        'isRecording': false,
        'sessionId': null,
        'actionsCount': 0,
        'snapshotsCount': 0,
        'duration': 0,
      };
    }

    final duration = _currentSession!.endTime != null
        ? _currentSession!.endTime! - _currentSession!.startTime
        : DateTime.now().millisecondsSinceEpoch - _currentSession!.startTime;

    return {
      'isRecording': _currentSession!.endTime == null,
      'sessionId': _currentSession!.sessionId,
      'actionsCount': _currentSession!.actions.length,
      'snapshotsCount': _currentSession!.snapshots.length,
      'duration': duration,
    };
  }
}