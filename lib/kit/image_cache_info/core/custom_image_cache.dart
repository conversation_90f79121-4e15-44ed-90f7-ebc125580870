import 'package:flutter/widgets.dart';
import 'package:golden_eye/kit/image_cache_info/image_cache_info_kit.dart';

import 'custom_navigator_observer.dart';
import 'package:golden_eye/util/int_extension.dart';

/// 一个基于路由管理图像缓存的自定义图像缓存。
/// 此缓存将图像与特定路由关联，并在路由不再活动时驱逐它们。
class CustomImageCache extends ImageCache {

  /// 将路由名称映射到与该路由关联的图像键集。
  final Map<String, Set<Object>> _routeToImageKeys = {};
  /// 将路由名称映射到该路由的总缓存大小（以字节为单位）。
  final Map<String, int> _routeToCacheSize = {};
  /// 包含当前所有缓存图像键的集合。
  final Set<Object> _allImageKeys = {};

  static CustomImageCache instance = CustomImageCache._();

  CustomImageCache._();

  Map<String, int> get routeToCacheSize => _routeToCacheSize;

  @override
  ImageStreamCompleter? putIfAbsent(
      Object key, ImageStreamCompleter Function() loader,
      {ImageErrorListener? onError}) {
    final completer = super.putIfAbsent(key, loader, onError: onError);

    if (!_isKeyUniqueToRoute(key)) {
      /// 已经映射过之前的路由
      return completer;
    }

    /// 取得当前栈顶的路由
    final lastRoute = CustomNavigatorObserver.instance.lastRouter;
    final lastRouteName = lastRoute?.settings.name;
    if (lastRouteName != null) {
      _addImageKeyForRoute(lastRouteName, key);
      _calculateCacheSize(lastRouteName, completer);
    }

    return completer;
  }

  /// 检查给定的图像键是否已经映射过之前的路由。
  bool _isKeyUniqueToRoute(Object imageKey) {
    return !_allImageKeys.contains(imageKey);
  }

  /// 将图像键添加到与给定路由关联的集合中。
  void _addImageKeyForRoute(String routeName, Object imageKey) {
    /// 与该路由关联的图像键集如果不存在，则创建一个
    _routeToImageKeys.putIfAbsent(routeName, () => <Object>{});
    /// 将图像键添加到与该路由关联的图像键集
    _routeToImageKeys[routeName]!.add(imageKey);
    /// 记录已经映射过的图像键
    _allImageKeys.add(imageKey);
    debugPrint(
        'addImageKeyForRoute: imageKey = $imageKey, _routeToImageKeys = $_routeToImageKeys');
  }

  /// 计算并更新给定路由的缓存大小。
  void _calculateCacheSize(String routeName, ImageStreamCompleter? completer) {
    late ImageStreamListener streamListener;
    void listener(ImageInfo? info, bool syncCall) {
      if (info != null) {
        int sizeBytes = info.sizeBytes;
        info.dispose();

        final currentSizeBytes = _routeToCacheSize[routeName] ?? 0;
        _routeToCacheSize[routeName] = currentSizeBytes + sizeBytes;

        completer?.removeListener(streamListener);

        /// 通知监听器更新给定路由的缓存大小
        ImageCacheInfoKit.notifier.notifyListeners();
        debugPrint(
            '_calculateCacheSize: _routeSizeBytes = ${_routeToCacheSize[routeName]?.prettySize()}');
      }
    }
    streamListener = ImageStreamListener(listener);
    completer?.addListener(streamListener);
  }

  /// 从缓存中驱逐与指定路由关联的图像。
  void evictByRoute(String routeName) {
    final keys = _routeToImageKeys[routeName];
    if (keys != null) {
      for (final key in keys) {
        super.evict(key, includeLive: false);
        _allImageKeys.remove(key); // 从所有缓存键集合中移除
      }
    }
    _routeToImageKeys.remove(routeName);
    _routeToCacheSize.remove(routeName);
    /// 通知监听器更新给定路由的缓存大小
    ImageCacheInfoKit.notifier.notifyListeners();

    debugPrint(
        'evictByRoute: imageCache.currentSize = $currentSize, imageCache.currentSizeBytes = ${currentSizeBytes.prettySize()}, _routeToImageKeys = $_routeToImageKeys');
  }
}
