import 'package:flutter/material.dart';

import 'custom_image_cache.dart';

class CustomNavigatorObserver extends NavigatorObserver {

  static CustomNavigatorObserver instance = CustomNavigatorObserver._();

  CustomNavigatorObserver._();

  /// 存储当前路由栈
  final List<Route<dynamic>> _routes = [];
  /// 获取栈顶的路由
  Route<dynamic>? get lastRouter => _routes.isNotEmpty ? _routes.last : null;

  BuildContext getLastContext() {
    assert(_routes.isNotEmpty, "please set MaterialApp navigatorObservers!!");
    return _routes.last.navigator!.context;
  }

  OverlayState? getLastOverlay() {
    return _routes.last.navigator?.overlay;
  }

  /// 获取界面栈内name == path 的route对象
  List<Route<dynamic>> getRoutes({String? path}) {
    final List<Route<dynamic>> routes = [];
    for (var route in _routes) {
      if (null != path && route.settings.name == path) {
        routes.add(route);
      } else if (null == path) {
        routes.add(route);
      }
    }
    return routes;
  }

  /// 获取name 包含 containPath 的route 对象
  List<Route<dynamic>> getContainRoutes({required String containPath}) {
    final List<Route<dynamic>> routes = [];
    for (var route in _routes) {
      var routeName = route.settings.name;
      if (null != routeName && routeName.contains(containPath)) {
        routes.add(route);
      }
    }
    return routes;
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    _routes.remove(route);

    final imageCache = PaintingBinding.instance.imageCache;
    if (imageCache is CustomImageCache) {
      imageCache.evictByRoute(route.settings.name ?? "unknown");
    }
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _routes.add(route);
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    _routes.remove(route);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);

    if (null != newRoute && null != oldRoute) {
      int index = _routes.indexOf(oldRoute);
      _routes[index] = newRoute;
    }
  }
}
