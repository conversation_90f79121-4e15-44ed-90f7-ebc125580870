import 'package:flutter/material.dart';

class Bar<PERSON>hart extends StatelessWidget {
  final List<double> data;
  final List<String> xAxis;

  const BarChart({super.key, required this.data, required this.xAxis});

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: 1,
      child: CustomPaint(
        painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(data, xAxis),
        size: const Si<PERSON>(300, 300),
      ),
    );
  }
}

class BarChartPainter extends CustomPainter {
  final List<double> data;
  final List<String> xAxis;

  BarChartPainter(this.data, this.xAxis);

  @override
  void paint(Canvas canvas, Size size) {
    _drawAxis(canvas, size);
    _drawLabels(canvas, size);
    _drawBars(canvas, size);
  }

  void _drawAxis(Canvas canvas, Size size) {
    final double sw = size.width;
    final double sh = size.height;

    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(0, sh)
      ..lineTo(sw, sh);

    canvas.drawPath(path, paint);
  }

  void _drawLabels(Canvas canvas, Size size) {
    if(data.isEmpty) return;

    final double sh = size.height;
    final double yAxisGap = sh / (data.length + 1);
    const labelFontSize = 14.0;

    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1;

    final maxData =
        data.reduce((value, element) => value > element ? value : element);
    final dataGap = maxData / data.length;

    List<double> yAxisLabel = <double>[];
    for (int i = 1; i <= data.length; i++) {
      yAxisLabel.add(i * dataGap);
    }
    yAxisLabel = yAxisLabel.reversed.toList();

    canvas.translate(0, yAxisGap);
    for (int i = 0; i < data.length; i++) {
      final double y = i * yAxisGap;
      canvas.drawLine(Offset(0, y), Offset(5, y), paint);

      final Offset textOffset = Offset(
        -labelFontSize * 2,
        y - labelFontSize / 2,
      );
      TextPainter(
        text: TextSpan(
          text: yAxisLabel[i].toStringAsFixed(1),
          style: const TextStyle(color: Colors.white, fontSize: labelFontSize),
        ),
        textAlign: TextAlign.right,
        textDirection: TextDirection.ltr,
        textWidthBasis: TextWidthBasis.longestLine,
      )
        ..layout(minWidth: 0, maxWidth: 50)
        ..paint(canvas, textOffset);
    }

    TextPainter(
      text: const TextSpan(
        text: 'Unit：MB',
        style: TextStyle(color: Colors.white, fontSize: labelFontSize),
      ),
      textAlign: TextAlign.right,
      textDirection: TextDirection.ltr,
      textWidthBasis: TextWidthBasis.longestLine,
    )
      ..layout(minWidth: 0, maxWidth: 100)
      ..paint(canvas, Offset(10, -yAxisGap));
  }

  List<Color> colors = [
    Color(0xff8e43e7),
    Color(0xffff4f81),
    Color(0xff1cc7d0),
    Color(0xff00aeff),
    Color(0xff3369e7),
    Color(0xffb84592),
    Color(0xff2dde98),
    Color(0xffff6c5f),
    Color(0xff003666),
    Color(0xffffc168),
    Color(0xff050f2c),
  ];

  void _drawBars(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    const textFontSize = 14.0;
    final double sw = size.width;
    final double sh = size.height;

    final xAxisGap = sw / (data.length + 1) * (0.3);
    final double yAxisGap = sh / (data.length + 1);
    final barWidth = sw / (data.length + 1) * (0.7);

    final paint = Paint()
      ..strokeWidth = 1
      ..style = PaintingStyle.fill;

    final maxData =
        data.reduce((value, element) => value > element ? value : element);

    canvas.translate(xAxisGap, 0);
    for (int i = 0; i < data.length; i++) {
      paint.color = colors[i];

      final left = i * (barWidth + xAxisGap);
      final height = (sh - yAxisGap) * data[i] / maxData;
      final top = sh - yAxisGap - height;
      final rect = Rect.fromLTWH(left, top, barWidth, height);
      canvas.drawRect(rect, paint);

      final offset = Offset(
        left + barWidth / 2 - textFontSize * 1.2,
        top - textFontSize * 2,
      );
      TextPainter(
        text: TextSpan(
          text: data[i].toStringAsFixed(1),
          style: TextStyle(fontSize: textFontSize, color: paint.color),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      )
        ..layout(
          minWidth: 0,
          maxWidth: textFontSize * data.toString().length,
        )
        ..paint(canvas, offset);

      final xOffset =
          Offset(left + barWidth / 2 - textFontSize, sh - yAxisGap + 12);
      // 绘制横轴标识
      TextPainter(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: xAxis[i].toString(),
          style: TextStyle(fontSize: 12, color: Colors.white),
        ),
        textDirection: TextDirection.ltr,
      )
        ..layout(
          minWidth: 0,
          maxWidth: size.width,
        )
        ..paint(canvas, xOffset);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
