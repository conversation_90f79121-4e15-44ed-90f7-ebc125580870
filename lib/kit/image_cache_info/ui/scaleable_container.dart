import 'package:flutter/gestures.dart';
import 'package:flutter/widgets.dart';

class ScalableContainer extends StatefulWidget {
  final Widget child;
  final double minScale;
  final double maxScale;

  const ScalableContainer({
    Key? key,
    required this.child,
    this.minScale = 0.5,
    this.maxScale = 3.0,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ScalableContainerState();
}

class _ScalableContainerState extends State<ScalableContainer> {
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  double _initialScale = 1.0;
  Offset _lastFocalPoint = Offset.zero;
  bool _isScaling = false;

  void _onScaleStart(ScaleStartDetails details) {
    _initialScale = _scale;
    _lastFocalPoint = details.focalPoint;
    _isScaling = false;
  }

  void _onScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      // Handle scaling
      if (details.scale != 1.0) {
        _isScaling = true;
        _scale = (_initialScale * details.scale).clamp(widget.minScale, widget.maxScale);
      }

      // Handle translation (only if not currently scaling)
      if (!_isScaling || details.scale == 1.0) {
        final currentFocalPoint = details.focalPoint;
        final delta = currentFocalPoint - _lastFocalPoint;
        _offset += delta;
        _lastFocalPoint = currentFocalPoint;
      }
    });
  }

  void _onScaleEnd(ScaleEndDetails details) {
    _isScaling = false;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onScaleStart: _onScaleStart,
      onScaleUpdate: _onScaleUpdate,
      onScaleEnd: _onScaleEnd,
      child: Transform(
        transform: Matrix4.identity()
          ..translate(_offset.dx, _offset.dy)
          ..scale(_scale, _scale),
        alignment: Alignment.center,
        child: widget.child,
      ),
    );
  }
}