import 'package:flutter/material.dart';
import 'package:golden_eye/kit/image_cache_info/ui/bar_chart.dart';
import 'package:golden_eye/kit/image_cache_info/ui/scaleable_container.dart';
import 'package:golden_eye/util/int_extension.dart';

import 'core/custom_image_cache.dart';

class ImageCacheInfoKit {
  /// 是否开启图像缓存统计
  static bool enabled = false;

  /// 监听页面跳转变化
  static ValueNotifier<Map<String, int>> notifier = ValueNotifier({});

  /// 监听回调
  static VoidCallback? callback;

  /// 页面耗时显示 widget
  static final OverlayEntry _overlayEntry = OverlayEntry(builder: (context) {
    final routeToCacheSize = CustomImageCache.instance.routeToCacheSize;
    final child = Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          margin:
          const EdgeInsets.only(left: 12, right: 12, top: 24, bottom: 32),
          padding: const EdgeInsets.only(left: 16, top: 0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: Colors.black.withOpacity(0.85),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                      padding: const EdgeInsets.only(bottom: 8, top: 16),
                      child: const Text(
                        'Image Cache Info',
                        style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.red),
                      )),
                  Container(
                    constraints: BoxConstraints(
                        maxHeight: MediaQuery.of(context).size.height - 150),
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      child: Text(getImageCacheInfo(),
                          style: const TextStyle(
                            fontSize: 15,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                          strutStyle: const StrutStyle(
                              forceStrutHeight: true, height: 2)),
                    ),
                  ),
                  Transform.scale(
                      scale: 0.8,
                      child: BarChart(
                          data: routeToCacheSize.values
                              .map((e) => e.toDouble() / (1024 * 1024))
                              .toList(),
                          xAxis: routeToCacheSize.keys.toList())),
                ],
              ),
              Positioned(
                top: 0,
                right: 0,
                child: CloseButton(
                  onPressed: () {
                    ImageCacheInfoKit.close();
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
    return ScalableContainer(child: child);
  });

  static void open(BuildContext context) {
    enabled = true;
    callback = () {
      _overlayEntry.markNeedsBuild();
    };
    notifier.addListener(callback!);

    Navigator.of(context).overlay?.insert(_overlayEntry);
  }

  static void close() {
    enabled = false;
    if (callback != null) {
      notifier.removeListener(callback!);
    }
    _overlayEntry.remove();
  }

  static String getImageCacheInfo() {
    Map dataMap = {};
    dataMap.addAll(readImageCacheInfo());
    StringBuffer buffer = StringBuffer();
    for (int i = 0; i < dataMap.length; i++) {
      buffer.write('${dataMap.keys.elementAt(i)}:  ${dataMap.values.elementAt(i)}');
      if (i != dataMap.length - 1) {
        buffer.write('\n');
      }
    }
    return buffer.toString();
  }

  static Map<String, dynamic> readImageCacheInfo() {
    return <String, dynamic>{
      'currentSizeBytes':
          PaintingBinding.instance.imageCache.currentSizeBytes.prettySize(),
      'currentSize': PaintingBinding.instance.imageCache.currentSize,
      'liveImageCount': PaintingBinding.instance.imageCache.liveImageCount,
    };
  }
}
