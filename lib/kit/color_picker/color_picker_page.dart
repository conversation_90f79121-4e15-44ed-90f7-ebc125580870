import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:golden_eye/kit/widget_info/ui/close_overlay_widget.dart';
import 'package:golden_eye/ui/global.dart';

class ColorPickerTool {
  static final OverlayEntry _overlayEntry = OverlayEntry(
    builder: (context) => PixelColorPage(repaintBoundaryKey: rootKey),
  );

  static final OverlayEntry _closeOverlayEntry =
      OverlayEntry(builder: (context) {
    return CloseOverlayWidget(onTap: () => removeColorPickerToolOverlay());
  });

  static void showColorPickerOverlay(BuildContext context) {
    OverlayState overlayState = Overlay.of(context);

    overlayState.insert(_overlayEntry);
    overlayState.insert(_closeOverlayEntry);
  }

  static void removeColorPickerToolOverlay() {
    _overlayEntry.remove();
    _closeOverlayEntry.remove();
  }
}

class PixelColorPage extends StatefulWidget {
  final GlobalKey repaintBoundaryKey;

  const PixelColorPage({required this.repaintBoundaryKey, super.key});

  @override
  State<PixelColorPage> createState() => _PixelColorPageState();
}

class _PixelColorPageState extends State<PixelColorPage> {
  Color? _pickedColor;
  Offset? _touchPosition;
  ui.Image? _capturedImage;
  ByteData? _byteData;
  static const double _magnifierSize = 100; // 放大镜大小
  static const double _magnifierZoom = 2.0; // 放大倍数

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance
        .addPostFrameCallback((_) => _captureRepaintBoundary());
  }

  @override
  void dispose() {
    _capturedImage?.dispose();
    super.dispose();
  }

  Future<void> _captureRepaintBoundary() async {
    try {
      final RenderRepaintBoundary boundary =
          widget.repaintBoundaryKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage();
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.rawRgba);
      if (mounted) {
        setState(() {
          _byteData = byteData;
          _capturedImage = image;
        });
      }
    } catch (e) {
      debugPrint('Error capturing boundary: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_capturedImage == null || _byteData == null) {
      return const SizedBox.shrink();
    }
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onPanDown: (details) => _updateColor(details.globalPosition),
        onPanUpdate: (details) => _updateColor(details.globalPosition),
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                color: Colors.transparent,
              ),
            ),
            // 放大镜
            if (_touchPosition != null && _capturedImage != null)
              Positioned(
                left: _adjustMagnifierPositionX(_touchPosition!.dx, context),
                top: _adjustMagnifierPositionY(_touchPosition!.dy, context),
                child: CustomPaint(
                  size: const Size(_magnifierSize, _magnifierSize),
                  painter: _MagnifierPainter(
                    image: _capturedImage!,
                    position: _touchPosition!,
                    zoom: _magnifierZoom,
                    magnifierSize: _magnifierSize,
                  ),
                ),
              ),
            // 显示颜色值
            if (_pickedColor != null && _touchPosition != null)
              Positioned(
                left: _adjustColorPositionX(_touchPosition!.dx, context),
                top: _adjustColorPositionY(_touchPosition!.dy, context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.black),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Color: (${_pickedColor!.red}, ${_pickedColor!.green}, ${_pickedColor!.blue}, ${_pickedColor!.alpha})',
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 10),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                            color: _pickedColor,
                            border: Border.all(color: Colors.black, width: 1)),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateColor(Offset globalPosition) async {
    if (_capturedImage == null || _byteData == null) return;

    try {
      if (mounted) {
        setState(() {
          _touchPosition = globalPosition;
        });
      }

      Offset localPosition = globalPosition;
      localPosition = Offset(
        localPosition.dx.clamp(0, _capturedImage!.width.toDouble() - 1),
        localPosition.dy.clamp(0, _capturedImage!.height.toDouble() - 1),
      );

      final pixelIndex = ((localPosition.dy.toInt() * _capturedImage!.width) +
              localPosition.dx.toInt()) *
          4;
      if (pixelIndex < 0 || pixelIndex >= _byteData!.lengthInBytes) return;

      Uint8List pixels = _byteData!.buffer.asUint8List();

      // debugPrint('Local Position: $localPosition');
      // debugPrint(
      //     'Image Dimensions: ${_capturedImage!.width} x ${_capturedImage!.height}');
      // debugPrint('Pixel Index: $pixelIndex');

      if (mounted) {
        setState(() {
          _pickedColor = Color.fromARGB(
            pixels[pixelIndex + 3], // Alpha
            pixels[pixelIndex], // Red
            pixels[pixelIndex + 1], // Green
            pixels[pixelIndex + 2], // Blue
          );
        });
      }
    } catch (e) {
      debugPrint('Error picking color: $e');
    }
  }

  double _adjustMagnifierPositionX(double x, BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (x - _magnifierSize / 2 < 0) return 0;
    if (x + _magnifierSize / 2 > screenWidth) {
      return screenWidth - _magnifierSize;
    }
    return x - _magnifierSize / 2;
  }

  double _adjustMagnifierPositionY(double y, BuildContext context) {
    if (y - _magnifierSize - 10 < 0) return 10; // 超出顶部
    return y - _magnifierSize - 10;
  }

  double _adjustColorPositionX(double x, BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (x + 200 > screenWidth) return screenWidth - 200;
    return x + 20;
  }

  double _adjustColorPositionY(double y, BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (y + 80 > screenHeight) return screenHeight - 250;
    return y + 20;
  }
}

class _MagnifierPainter extends CustomPainter {
  final ui.Image image;
  final Offset position;
  final double zoom;
  final double magnifierSize;

  _MagnifierPainter({
    required this.image,
    required this.position,
    required this.zoom,
    required this.magnifierSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // 计算放大镜区域
    final srcRect = Rect.fromLTWH(
      position.dx - magnifierSize / (2 * zoom),
      position.dy - magnifierSize / (2 * zoom),
      magnifierSize / zoom,
      magnifierSize / zoom,
    );

    final dstRect = Rect.fromLTWH(0, 0, magnifierSize, magnifierSize);

    canvas.drawImageRect(image, srcRect, dstRect, paint);

    paint
      ..style = PaintingStyle.stroke
      ..color = Colors.black
      ..strokeWidth = 2;
    canvas.drawCircle(size.center(Offset.zero), size.width / 2, paint);

    final point = Paint()
      ..style = PaintingStyle.stroke
      ..color = Colors.red
      ..strokeWidth = 1;
    canvas.drawCircle(size.center(Offset.zero), 2, point);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
