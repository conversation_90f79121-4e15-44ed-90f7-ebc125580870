import 'dart:convert';

import 'package:flutter/material.dart';

class JsonViewerPage extends StatefulWidget {
  final dynamic json;

  const JsonViewerPage({super.key, this.json});

  @override
  State<JsonViewerPage> createState() => _JsonViewerPageState();
}

class _JsonViewerPageState extends State<JsonViewerPage> {
  final TextEditingController _searchController = TextEditingController();

  dynamic _parsedJson;
  dynamic _searchResults;
  bool _isHighlightMode = false;

  @override
  void initState() {
    super.initState();

    _parseJson();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_parsedJson == null) const Center(child: Text('data is empty'));

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: <PERSON><PERSON>ield(
                controller: _searchController,
                maxLines: 1,
                style: const TextStyle(fontSize: 14),
                decoration: InputDecoration(
                  border: const OutlineInputBorder(),
                  hintText: 'Enter a keyword to search',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      _searchJson('');
                    },
                  ),
                ),
                onChanged: _searchJson,
              )),
              IconButton(
                icon: Icon(_isHighlightMode ? Icons.list : Icons.code),
                onPressed: () {
                  if (mounted) {
                    setState(() {
                      _isHighlightMode = !_isHighlightMode;
                    });
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _isHighlightMode
                ? _JsonCodeHighlighter(jsonData: _searchResults ?? _parsedJson)
                : JsonTreeViewer(
                    data: _searchResults ?? _parsedJson,
                    searchQuery: _searchController.text,
                  ),
          ),
        ],
      ),
    );
  }

  void _parseJson() {
    try {
      if (widget.json is Map<String, dynamic>) {
        _parsedJson = widget.json;
      } else {
        _parsedJson = jsonDecode(widget.json);
      }
      // 重置搜索结果
      _searchResults = null;
      if (mounted) setState(() {});
    } catch (e) {
      debugPrint('Invalid JSON data');
    }
  }

  void _searchJson(String query) {
    if (query.isEmpty) {
      if (mounted) {
        setState(() {
          // 显示完整数据
          _searchResults = null;
        });
      }
      return;
    }

    dynamic search(dynamic json, String currentPath) {
      if (json is Map) {
        final Map result = {};
        json.forEach((key, value) {
          final newPath = currentPath.isEmpty ? key : '$currentPath.$key';
          if (key.contains(query) || _valueContains(value, query)) {
            // 保存完整路径
            result[newPath] = value;
            result[newPath] = value;
          } else {
            final nestedResult = search(value, newPath);
            if (nestedResult != null) {
              result.addAll(nestedResult);
            }
          }
        });
        return result.isEmpty ? null : result;
      } else if (json is List) {
        final List result = [];
        for (var i = 0; i < json.length; i++) {
          final newPath = '$currentPath[$i]';
          final nestedResult = search(json[i], newPath);
          if (nestedResult != null) {
            result.add(nestedResult);
          }
        }
        return result.isEmpty ? null : result;
      }
      return null;
    }

    if (mounted) {
      setState(() {
        _searchResults = search(_parsedJson, '');
      });
    }
  }

  bool _valueContains(dynamic value, String query) {
    try {
      final regex = RegExp(query, caseSensitive: false); // 模糊匹配
      if (value is String) {
        return regex.hasMatch(value);
      } else if (value is num || value is bool) {
        return regex.hasMatch(value.toString());
      }
    } catch (e) {
      return value.toString().contains(query);
    }
    return false;
  }
}

class JsonTreeViewer extends StatelessWidget {
  final dynamic data;
  final String? searchQuery;

  const JsonTreeViewer({super.key, required this.data, this.searchQuery});

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        JsonNodeWidget(keyName: 'root', value: data, searchQuery: searchQuery)
      ],
    );
  }
}

class JsonNodeWidget extends StatefulWidget {
  final String? keyName;
  final dynamic value;
  final String? searchQuery;

  const JsonNodeWidget({super.key, this.keyName, this.value, this.searchQuery});

  @override
  State<JsonNodeWidget> createState() => _JsonNodeWidgetState();
}

class _JsonNodeWidgetState extends State<JsonNodeWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final bool hasChildren = widget.value is Map || widget.value is List;
    const keyColor = Colors.blue;
    final Color valueColor = widget.value is String
        ? Colors.green
        : widget.value is num
            ? Colors.orange
            : Colors.purple;

    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: hasChildren ? _toggleExpand : null,
            child: Row(
              children: [
                if (hasChildren)
                  Icon(
                    _isExpanded ? Icons.expand_more : Icons.chevron_right,
                    size: 16,
                  ),
                if (widget.keyName != null)
                  Text('"${widget.keyName}"',
                      style: const TextStyle(color: keyColor)),
                if (widget.keyName != null) const Text(': '),
                if (!hasChildren)
                  Expanded(
                    child: widget.value is String ||
                            widget.value is num ||
                            widget.value is bool
                        ? _highlightText(widget.value.toString(),
                            widget.searchQuery, valueColor)
                        : Text(widget.value.toString(),
                            style: TextStyle(color: valueColor)),
                  ),
              ],
            ),
          ),
          if (_isExpanded)
            if (widget.value is Map)
              ..._buildMapChildren(widget.value as Map)
            else if (widget.value is List)
              ..._buildListChildren(widget.value as List),
        ],
      ),
    );
  }

  void _toggleExpand() {
    if (mounted) {
      setState(() {
        _isExpanded = !_isExpanded;
      });
    }
  }

  List<Widget> _buildMapChildren(Map map) {
    return map.entries
        .map((entry) => JsonNodeWidget(
              keyName: entry.key.toString(),
              value: entry.value,
              searchQuery: widget.searchQuery,
            ))
        .toList();
  }

  List<Widget> _buildListChildren(List list) {
    return list
        .asMap()
        .entries
        .map((entry) => JsonNodeWidget(
              keyName: '[${entry.key}]',
              value: entry.value,
              searchQuery: widget.searchQuery,
            ))
        .toList();
  }

  Widget _highlightText(String text, String? query, Color valueColor) {
    if (query == null || query.isEmpty) {
      return Text(text, style: TextStyle(color: valueColor));
    }

    try {
      final regex = RegExp(query, caseSensitive: false);
      final matches = regex.allMatches(text);

      if (matches.isEmpty) {
        return Text(text, style: TextStyle(color: valueColor));
      }

      final List<TextSpan> spans = [];
      int lastMatchEnd = 0;

      for (final match in matches) {
        if (match.start > lastMatchEnd) {
          spans.add(TextSpan(
              text: text.substring(lastMatchEnd, match.start),
              style: TextStyle(color: valueColor)));
        }
        spans.add(TextSpan(
            text: text.substring(match.start, match.end),
            style: const TextStyle(
                color: Colors.red, fontWeight: FontWeight.bold)));
        lastMatchEnd = match.end;
      }

      if (lastMatchEnd < text.length) {
        spans.add(TextSpan(
            text: text.substring(lastMatchEnd),
            style: TextStyle(color: valueColor)));
      }

      return RichText(text: TextSpan(children: spans));
    } catch (e) {
      return Text(text, style: TextStyle(color: valueColor));
    }
  }
}

class _JsonCodeHighlighter extends StatelessWidget {
  final dynamic jsonData;

  const _JsonCodeHighlighter({required this.jsonData});

  @override
  Widget build(BuildContext context) {
    final formattedJson = const JsonEncoder.withIndent('  ').convert(jsonData);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(12),
      child: SelectableText(
        formattedJson,
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 14,
        ),
      ),
    );
  }
}
