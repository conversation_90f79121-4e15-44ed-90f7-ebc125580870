import 'package:flutter/material.dart';
import 'package:golden_eye/kit/widget_info/ui/close_overlay_widget.dart';

class AlignmentTool {
  static final OverlayEntry _overlayEntry = OverlayEntry(builder: (context) {
    return const _AlignmentWidget();
  });

  static final OverlayEntry _closeOverlayEntry =
      OverlayEntry(builder: (context) {
    return CloseOverlayWidget(onTap: () => removeAlignmentToolOverlay());
  });

  static void showAlignmentToolOverlay(BuildContext context) {
    OverlayState overlayState = Overlay.of(context);
    overlayState.insert(_overlayEntry);
    overlayState.insert(_closeOverlayEntry);
  }

  static void removeAlignmentToolOverlay() {
    _overlayEntry.remove();
    _closeOverlayEntry.remove();
  }
}

class _AlignmentWidget extends StatefulWidget {
  const _AlignmentWidget();

  @override
  State<_AlignmentWidget> createState() => _AlignmentWidgetState();
}

class _AlignmentWidgetState extends State<_AlignmentWidget> {
  Offset _circlePosition = const Offset(200, 200);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    final topDistance = _circlePosition.dy;
    final bottomDistance = screenHeight - _circlePosition.dy;
    final leftDistance = _circlePosition.dx;
    final rightDistance = screenWidth - _circlePosition.dx;

    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onPanUpdate: (details) {
          if (mounted) {
            setState(() {
              // 更新圆心位置
              _circlePosition += details.delta;
            });
          }
        },
        child: Stack(
          children: [
            Positioned.fill(
              child: CustomPaint(
                painter: _BaselinePainter(circlePosition: _circlePosition),
              ),
            ),
            Positioned(
              left: _circlePosition.dx - 20,
              top: _circlePosition.dy - 20,
              child: Container(
                alignment: Alignment.center,
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.red, width: 1),
                ),
                child: Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.red, width: 1),
                  ),
                ),
              ),
            ),
            Positioned(
              left: _adjustPositionX(_circlePosition.dx - 50, screenWidth),
              top: _adjustPositionY(_circlePosition.dy - 100, screenHeight),
              child: Text(
                'top:${topDistance.toStringAsFixed(1)}',
                style: const TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold),
              ),
            ),
            Positioned(
              left: _adjustPositionX(_circlePosition.dx - 50, screenWidth),
              top: _adjustPositionY(_circlePosition.dy + 100, screenHeight),
              child: Text(
                'bottom:${bottomDistance.toStringAsFixed(1)}',
                style: const TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold),
              ),
            ),
            Positioned(
              left: _adjustPositionX(_circlePosition.dx - 180, screenWidth),
              top: _adjustPositionY(_circlePosition.dy - 10, screenHeight),
              child: Text(
                'left:${leftDistance.toStringAsFixed(1)}',
                style: const TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold),
              ),
            ),
            Positioned(
              left: _adjustPositionX(_circlePosition.dx + 180, screenWidth),
              top: _adjustPositionY(_circlePosition.dy - 10, screenHeight),
              child: Text(
                'right:${rightDistance.toStringAsFixed(1)}',
                style: const TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _adjustPositionX(double x, double screenWidth) {
    if (x < 0) return 50;
    if (x > screenWidth - 100) return screenWidth - 100;
    return x;
  }

  double _adjustPositionY(double y, double screenHeight) {
    if (y < 0) return 0;
    if (y > screenHeight - 20) return screenHeight - 20;
    return y;
  }
}

class _BaselinePainter extends CustomPainter {
  final Offset circlePosition;

  _BaselinePainter({required this.circlePosition});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.red
      ..strokeWidth = 1;

    canvas.drawLine(
      Offset(0, circlePosition.dy),
      Offset(size.width, circlePosition.dy),
      paint,
    );

    canvas.drawLine(
      Offset(circlePosition.dx, 0),
      Offset(circlePosition.dx, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
