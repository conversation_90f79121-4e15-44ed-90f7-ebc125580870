import 'package:vm_service/vm_service.dart';
import 'package:vm_service/vm_service.dart';
import '../../../service/vm_service/service_mixin.dart';

/// 用于显示页面源码的服务。
class CodeDisplayService with VMServiceWrapper {

  /// 根据类名获取script id。
  /// 该方法会遍历所有的类，以类名进行匹配。
  Future<String?> getScriptIdWithClassName(String className) async {
    final classList = await serviceWrapper.getClassList();
    final classes = classList.classes;
    if (classes == null) return null;

    for (final cls in classes) {
      if (cls.name == className) {
        return cls.location?.script?.id;
      }
    }

    return null;
  }

  /// 根据文件名获取script id。
  /// 该方法会遍历所有的Script，将文件名与script的uri进行匹配。
  Future<String?> getScriptIdWithFileName(String fileName) async {
    ScriptList scriptList = await serviceWrapper.getScripts();
    final scripts = scriptList.scripts!;

    for (final script in scripts) {
      if (script.uri!.contains(fileName)) return script.id;
    }

    return null;
  }

  /// 根据关键词获取script id的map。
  /// 该方法会遍历所有的Script，并将关键词与script的uri进行匹配。
  Future<Map<String?, String?>> getScriptIdsWithKeyword(String keyword) async {
    ScriptList scriptList = await serviceWrapper.getScripts();

    var result = <String?, String?>{};

    scriptList.scripts!.forEach((script) {
      if (script.uri!.contains(keyword)) {
        result[script.id] = script.uri;
      }
    });
    return result;
  }

  /// 根据script id获取源代码。
  /// 该方法会根据script id获取对应的Script对象，并将其source字段返回。
  Future<String?> getSourceCodeWithScriptId(String scriptId) async {
    Obj script = await serviceWrapper.getObject(scriptId);
    if (script is Script) {
      return script.source;
    }
    return null;
  }

  /// 根据script id获取对应的Script对象。
  Future<Script?> getScriptWithScriptId(String scriptId) async {
    Obj script = await serviceWrapper.getObject(scriptId);
    if (script is Script) {
      return script;
    }
    return null;
  }

  /// 获取库列表以及库下的类源码。
  Future<List<LibraryRef>?> getLibrariesWithClasses() async {
    final libraries = await serviceWrapper.getLibraries();
    return libraries;
  }

}