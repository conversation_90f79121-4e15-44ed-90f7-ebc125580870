import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:vm_service/vm_service.dart';

import '../../../ui/global.dart';
import '../ui/program_explorer_model.dart';
import 'code_display_service.dart';

/// 页面信息帮助类，用于获取当前页面的的源码
class PageInfoHelper {
  PageInfoHelper() {
    _selectionInit();
  }

  /// 当前被选中的对象
  final InspectorSelection selection =
      WidgetInspectorService.instance.selection;

  /// 被选中对象的RenderObject
  RenderObject? get renderObject => selection.current;

  /// 被选中对象的Element
  Element? get element => selection.currentElement;

  /// 被选中对象的类文件路径
  String? get filePath => _jsonInfo!['creationLocation']['file'];

  /// 将路径转换为package:模式
  String packagePathConvertFromFilePath(String filePath) {
    final parts = filePath.split(r'/lib/');
    final fileForwardPart = parts.sublist(1).join('/lib/');
    final packageName = parts.first.split('/').last;
    final keyword = "package:$packageName/$fileForwardPart";
    CodeDisplayService().getScriptIdsWithKeyword(keyword);
    debugPrint(keyword);
    return keyword;
  }

  /// 被选中对象在类文件中的行号
  int? get line => _jsonInfo!['creationLocation']['line'];

  /// ignorePointer的对象
  dynamic _ignorePointer;

  /// 被选中对象的信息
  String get message {
    return '''${element!.toStringShort()}\nsize: ${renderObject!.paintBounds.size}\nfilePath: $filePath\nline: $line''';
  }

  /// 获取当前被选中的对象的WidgetId，并根据WidgetId获取完整的Json信息
  Map? get _jsonInfo {
    if (renderObject == null) return null;
    final widgetId = WidgetInspectorService.instance
        // ignore: invalid_use_of_protected_member
        .toId(renderObject!.toDiagnosticsNode(), '');
    if (widgetId == null) return null;

    String infoStr =
        WidgetInspectorService.instance.getSelectedSummaryWidget(widgetId, '');
    return json.decode(infoStr);
  }

  /// 获取被选中对象的轮廓
  double _area(RenderObject object) {
    final Size size = object.paintBounds.size;
    return size == null ? double.maxFinite : size.width * size.height;
  }

  /// 初始化当前页面的被选中对象
  /// 从RootWidget的RenderObject开始，向下查找所有的RenderObject，作为候选对象
  void _selectionInit() {
    _ignorePointer = rootKey.currentContext!.findRenderObject();
    final RenderObject userRender = _ignorePointer.child;
    List<RenderObject> objectList = [];

    void findAllRenderObject(RenderObject object) {
      final List<DiagnosticsNode> children = object.debugDescribeChildren();
      for (int i = 0; i < children.length; i++) {
        DiagnosticsNode c = children[i];
        if (c.style == DiagnosticsTreeStyle.offstage || c.value is! RenderBox)
          continue;
        RenderObject child = c.value as RenderObject;
        objectList.add(child);
        findAllRenderObject(child);
      }
    }

    findAllRenderObject(userRender);
    objectList
        .sort((RenderObject a, RenderObject b) =>
        _area(a).compareTo(_area(b)));
    Set<RenderObject> objectSet = Set<RenderObject>();
    objectSet.addAll(objectList);
    objectList = objectSet.toList();
    selection.candidates = objectList;
  }

  /// 获取当前页面的源码
  Future<String?> getCode() async {
    CodeDisplayService codeDisplayService = CodeDisplayService();
    String targetFileName = filePath!.split('/').last;
    String? scriptId =
        await codeDisplayService.getScriptIdWithFileName(targetFileName);
    if (scriptId == null) return null;
    String? sourceCode =
        await codeDisplayService.getSourceCodeWithScriptId(scriptId);
    return sourceCode;
  }

  /// 通过文件名获取源码
  Future<String?> getCodeByFileName(String fileName) async {
    CodeDisplayService codeDisplayService = CodeDisplayService();
    String? sourceCode;
    String? scriptId =
        await codeDisplayService.getScriptIdWithFileName(fileName);
    if (scriptId != null) {
      sourceCode = await codeDisplayService.getSourceCodeWithScriptId(scriptId);
    }
    return sourceCode;
  }

  /// 通过关键字获取源码列表
  Future<Map<String?, String>> getCodeListByKeyword(String keyword) async {
    CodeDisplayService codeDisplayService = CodeDisplayService();
    Map<String?, String> result = <String?, String>{};
    final scriptIds = await codeDisplayService.getScriptIdsWithKeyword(keyword);
    if (scriptIds.isNotEmpty) {
      for (final entry in scriptIds.entries) {
        final code =
            await codeDisplayService.getSourceCodeWithScriptId(entry.key!);
        if (code != null && code.isNotEmpty) {
          result[entry.value] = code;
        }
      }
    }
    return result;
  }

  /// 通过类名获取源码
  Future<Map<String?, String>> getCodeByClassName(String fileName) async {
    CodeDisplayService codeDisplayService = CodeDisplayService();
    Map<String?, String> result = <String?, String>{};
    Script? script;
    String? scriptId =
        await codeDisplayService.getScriptIdWithClassName(fileName);
    if (scriptId != null) {
      script = await codeDisplayService.getScriptWithScriptId(scriptId);
      if (script?.uri != null &&
          script?.uri?.isNotEmpty == true &&
          script?.source != null &&
          script?.source?.isNotEmpty == true) {
        result[script!.uri] = script.source!;
      }
    }
    return result;
  }

  /// 获取库列表以及库下的类源码。
  Future<VMServiceObjectNode> getCodeTree() async {
    CodeDisplayService codeDisplayService = CodeDisplayService();
    final libraries = await codeDisplayService.getLibrariesWithClasses();
    final root = VMServiceObjectNode.createRootsFrom(libraries);
    return root;
  }
}