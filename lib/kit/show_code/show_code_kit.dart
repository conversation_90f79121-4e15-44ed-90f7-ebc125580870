import 'package:flutter/material.dart';
import 'package:golden_eye/kit/show_code/core/page_info_helper.dart';
import 'package:golden_eye/kit/show_code/core/syntax_highlighter.dart';
import 'package:golden_eye/kit/show_code/ui/file_explorer.dart';
import 'package:golden_eye/kit/show_code/ui/program_explorer_model.dart';

class ShowCodeKit extends StatefulWidget {
  const ShowCodeKit({Key? key}) : super(key: key);

  @override
  ShowCodeKitState createState() => ShowCodeKitState();
}

class ShowCodeKitState extends State<ShowCodeKit> with WidgetsBindingObserver {
  late PageInfoHelper pageInfoHelper;
  String? code;
  String? filePath;
  VMServiceObjectNode? codeTree;

  Map<String?, String>? codeList;
  bool ShowCodeKitList = true;
  bool isSearching = true;
  TextEditingController? textEditingController;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      pageInfoHelper = PageInfoHelper();
      filePath = pageInfoHelper
          .packagePathConvertFromFilePath(pageInfoHelper.filePath!);
      pageInfoHelper.getCode().then((c) {
        code = c;
        setState(() {});
      });
      pageInfoHelper.getCodeTree().then((c) {
        codeTree = c;
        setState(() {});
      });
      ShowCodeKitList = false;
      isSearching = false;
      textEditingController = TextEditingController(text: filePath);
    });

    super.initState();
  }

  Widget _codeView() {
    String codeContent = code ?? '';
    if (codeList != null && codeList!.isNotEmpty && codeContent.isEmpty) {
      codeContent = '已找到匹配项，请点击菜单选择';
    }
    double _textScaleFactor = 1.0;
    final SyntaxHighlighterStyle style =
        Theme.of(context).brightness == Brightness.dark
            ? SyntaxHighlighterStyle.darkThemeStyle()
            : SyntaxHighlighterStyle.lightThemeStyle();
    return Scrollbar(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SelectableText.rich(
            TextSpan(
              style: TextStyle(fontFamily: 'monospace', fontSize: 12.0)
                  .apply(fontSizeFactor: _textScaleFactor),
              children: <TextSpan>[
                DartSyntaxHighlighter(style, (String className) {
                  setState(() {
                    isSearching = true;
                  });
                  pageInfoHelper.getCodeByClassName(className).then((result) {
                    if (result.isNotEmpty) {
                      code = result.values.first;
                      filePath = result.keys.first;
                      textEditingController!.text = filePath!;
                      isSearching = false;
                      setState(() {});
                    }
                  });
                }).format(codeContent)
              ],
            ),
            style: DefaultTextStyle.of(context)
                .style
                .apply(fontSizeFactor: _textScaleFactor),
          ),
        ),
      ),
    );
  }

  Widget _infoView() {
    return Container(
        padding: EdgeInsets.all(8),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const Text("页面代码", style: TextStyle(fontSize: 20, height: 1.5)),
              const Text.rich(
                TextSpan(children: [
                  TextSpan(
                    text: "当前路径（点击以编辑，支持部分匹配）：",
                  ),
                ], style: TextStyle(height: 2)),
              ),
              Row(
                children: <Widget>[
                  if (isSearching)
                    const SizedBox(
                      width: 22,
                      height: 22,
                      child: CircularProgressIndicator(),
                    ),
                  if (ShowCodeKitList &&
                      codeList != null &&
                      codeList!.isNotEmpty)
                    PopupMenuButton<String>(
                      padding: EdgeInsets.zero,
                      icon: Icon(Icons.arrow_drop_down),
                      onSelected: (String codepath) {
                        debugPrint(codepath);
                        setState(() {
                          code = codeList![codepath];
                          filePath = codepath;
                          textEditingController!.text = filePath!;
                        });
                      },
                      itemBuilder: (BuildContext context) => codeList!
                          .map((codepath, codeid) {
                            return MapEntry(
                              codepath,
                              PopupMenuItem<String>(
                                  value: codepath,
                                  child: Column(
                                    children: <Widget>[
                                      ListTile(
                                        title: Text(
                                          codepath!,
                                          style: TextStyle(
                                              color: Colors.teal, fontSize: 14),
                                        ),
                                      ),
                                      Divider(),
                                    ],
                                  )),
                            );
                          })
                          .values
                          .toList(),
                    ),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                          hintText: "请输入路径",
                          border: InputBorder.none,
                          suffixIcon: IconButton(
                            onPressed: () => textEditingController!.clear(),
                            icon: Icon(Icons.clear),
                          )),
                      controller: textEditingController,
                      style: TextStyle(color: Colors.teal, fontSize: 14),
                      maxLines: 5,
                      minLines: 1,
                      // decoration: null,
                      autocorrect: false,
                      enableSuggestions: false,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (value) {
                        if (value.length < 2) {
                          return;
                        }
                        setState(() {
                          isSearching = true;
                          filePath = value;
                        });
                        pageInfoHelper
                            .getCodeListByKeyword(value)
                            .then((codeList) {
                          if (codeList != null && codeList.isNotEmpty) {
                            ShowCodeKitList = true;
                            codeList = codeList;
                          } else {
                            ShowCodeKitList = false;
                          }
                          isSearching = false;
                          code = null;
                          filePath = null;
                          setState(() {});
                          debugPrint(codeList.length.toString());
                        });
                      },
                    ),
                  ),
                ],
              )
            ]));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: NavigationDrawer(
        children: <Widget>[
          FileExplorer(root: codeTree),
        ],
      ),
      body: Container(
          color: Colors.white,
          child: SafeArea(
              child: Column(
            children: <Widget>[
              _infoView(),
              Divider(),
              Expanded(
                flex: 1,
                child: _codeView(),
              )
            ],
          ))),
    );
  }
}
