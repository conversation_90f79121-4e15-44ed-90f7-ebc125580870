// Copyright 2021 The Flutter Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file or at https://developers.google.com/open-source/licenses/bsd.

import 'package:vm_service/vm_service.dart';

import 'trees.dart';

/// A node in a tree of VM service objects.
///
/// A node can represent one of the following:
///   - Directory
///   - Library (with or without a script)
///   - Script
///   - Class
///   - Field
///   - Function
///   - Code
class VMServiceObjectNode extends TreeNode<VMServiceObjectNode> {
  VMServiceObjectNode(this.name, {this.uri});

  static const dartPrefix = 'dart:';
  static const packagePrefix = 'package:';

  // final ProgramExplorerController controller;
  final String name;
  final String? uri;

  /// This exists to allow for O(1) lookup of children when building the tree.
  final _childrenAsMap = <String, VMServiceObjectNode>{};

  static VMServiceObjectNode createRootsFrom(
    List<LibraryRef>? libs,
  ) {
    // The name of this node is not exposed to users.
    final root = VMServiceObjectNode('<root>');
    if (libs == null || libs.isEmpty) return root;

    final sortedLibs = libs..sort((a, b) => a.uri!.toUpperCase().compareTo(b.uri!.toUpperCase()));

    for (final lib in sortedLibs) {
      _buildScriptNode(root, lib);
    }

    return root;
  }

  static VMServiceObjectNode _buildScriptNode(
      VMServiceObjectNode node, LibraryRef lib) {
    final parts = lib.uri!.split('/');
    final name = parts.removeLast();

    for (final part in parts) {
      // Directory nodes shouldn't be selectable unless they're a library node.
      node = node._lookupOrCreateChild(part, null);
    }

    node = node._lookupOrCreateChild(name, lib.uri);

    return node;
  }

  VMServiceObjectNode _lookupOrCreateChild(
    String name,
    String? uri,
  ) {
    return _childrenAsMap.putIfAbsent(
      name,
      () => _createChild(name, uri),
    );
  }

  VMServiceObjectNode _createChild(
    String name,
    String? uri,
  ) {
    final child = VMServiceObjectNode(name, uri: uri);
    addChild(child);
    return child;
  }

  @override
  TreeNode<VMServiceObjectNode> shallowCopy() {
    throw UnimplementedError(
      'This method is not implemented. Implement if you '
      'need to call `shallowCopy` on an instance of this class.',
    );
  }
}
