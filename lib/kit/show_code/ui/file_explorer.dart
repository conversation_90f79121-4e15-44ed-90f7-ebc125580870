import 'package:flutter/material.dart';
import 'package:golden_eye/kit/show_code/ui/tree_view.dart';

import '../../../util/list.dart';
import '../show_code_kit.dart';
import 'program_explorer_model.dart';

class FileExplorer extends StatefulWidget {
  FileExplorer({Key? key, this.root}) : super(key: key);

  final VMServiceObjectNode? root;

  @override
  State<FileExplorer> createState() => _FileExplorerState();
}

class _FileExplorerState extends State<FileExplorer> {
  @override
  Widget build(BuildContext context) {
    final rootObjectNodes = ListValueNotifier<VMServiceObjectNode>([]);
    rootObjectNodes.addAll(widget.root?.children ?? []);
    return TreeView<VMServiceObjectNode>(
      dataRootsListenable: rootObjectNodes,
      // onItemSelected: widget.onItemSelected,
      // onItemExpanded: widget.onItemExpanded,
      scrollController: ScrollController(),
      includeScrollbar: true,
      dataDisplayProvider: (node, onTap) {
        return _ProgramExplorerRow(
          node: node,
          onTap: () {
            onTap();
          },
        );
      },
    );
  }
}

class _ProgramExplorerRow extends StatelessWidget {
  const _ProgramExplorerRow({
    required this.node,
    required this.onTap,
  });

  final VMServiceObjectNode node;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        print("node.uri=${node.uri}");
        if (node.uri != null) {
          final showCodeKit = context.findAncestorStateOfType<ShowCodeKitState>();
          if (showCodeKit != null) {
            showCodeKit.setState(() {
              showCodeKit.isSearching = true;
              showCodeKit.filePath = node.uri;
              showCodeKit.textEditingController?.text = node.uri!;
            });
            
            showCodeKit.pageInfoHelper.getCodeListByKeyword(node.uri!).then((codeList) {
              if (codeList.isNotEmpty) {
                showCodeKit.setState(() {
                  showCodeKit.ShowCodeKitList = false;
                  showCodeKit.codeList = codeList;
                  showCodeKit.isSearching = false;
                  showCodeKit.code = codeList.values.first;
                  showCodeKit.filePath = codeList.keys.first;
                });
              }
            });
          }
        }
        onTap.call();
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.file_open, size: 15),
          const SizedBox(width: 3),
          Text(node.name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis, style: const TextStyle(fontSize: 14))
        ],
      ),
    );
  }
}
