import 'package:flutter/material.dart';
import 'package:golden_eye/data/req_model.dart';
import 'package:golden_eye/data/statistic_model.dart';
import 'package:golden_eye/ui/fps/fps_widget.dart';
import 'package:golden_eye/ui/traffic/traffic_btn.dart';

import 'traffic_data.dart';

final dataCenter = DataCenter._();

class DataCenter {
  DataCenter._();

  final List<FPSController> _fpsController = [];

  final List<HttpLogModel> _httpLogs = [];
  final List<StatisticModel> _statisticLogs = [];

  Function? channelListener;

  /// 网络流量监控
  final trafficData = TrafficData();

  void addHttpLog(HttpLogModel model) {
    if (_httpLogs.length > 99) {
      _httpLogs.removeAt(0);
    }
    _httpLogs.add(model);
  }

  void addStatisticLog(StatisticModel model) {
    if (_statisticLogs.length > 99) {
      _statisticLogs.removeAt(0);
    }
    _statisticLogs.add(model);
  }

  List<HttpLogModel> getHttpLogs() {
    return List<HttpLogModel>.from(_httpLogs).reversed.toList();
  }

  List<StatisticModel> getStatisticLogs() {
    return List<StatisticModel>.from(_statisticLogs).reversed.toList();
  }

  void addFpsController(FPSController controller) {
    _fpsController.add(controller);
  }

  void removeFpsController(FPSController controller) {
    _fpsController.remove(controller);
  }

  void fpsToggle() {
    for (var controller in _fpsController) {
      controller.toggle();
    }
  }

  void trafficToggle(BuildContext context, {VoidCallback? onTap}) {
    TrafficBtn.show(Navigator.of(context).overlay, onTap: onTap);
  }
}