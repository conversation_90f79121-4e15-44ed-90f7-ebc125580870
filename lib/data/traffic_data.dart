class TrafficData {

  int framesToDisplay = 10;

  /// 上行
  List<int> txList = [];

  /// 下行
  List<int> rxList = [];

  int totalTx = 0;
  int totalRx = 0;

  Map<String, TrafficDetailItem> detailMap = {};

  void setWidgetWidth(double width) {
    framesToDisplay = width ~/ 5;
  }

  void addTraffic(
      {required int rxTotal,
      required int txTotal,
      required List<TrafficDetailItem> details}) {
    totalRx += rxTotal;
    totalTx += txTotal;

    txList.add(txTotal);
    rxList.add(rxTotal);
    if (txList.length > framesToDisplay) {
      txList = txList.sublist(txList.length - framesToDisplay - 1);
      rxList = rxList.sublist(rxList.length - framesToDisplay - 1);
    }

    if (details.isNotEmpty) {
      for (final element in details) {
        if (detailMap.containsKey(element.url))  {
            detailMap[element.url]?.add(element);
        }  else {
          detailMap[element.url] = element;
        }
      }
    }

  }
}

class TrafficDetailItem {
  int count = 0;
  String url = '';

  /// 上行
  int txBytes = 0;

  /// 下行
  int rxBytes = 0;

  TrafficDetailItem(
      {this.count = 0, this.url = '', this.txBytes = 0, this.rxBytes = 0});

  void add(TrafficDetailItem item) {
    count += item.count;
    txBytes += item.txBytes;
    rxBytes += item.rxBytes;
  }
}
