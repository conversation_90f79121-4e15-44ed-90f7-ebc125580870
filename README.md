# golden_eye



## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!


## Integrate with your tools

- 方便测试使用的工具集
  1，近100条的请求日志， 格式化路径，入参，出参，耗时
  2，近100条打点，格式化action，公参，带clear，搜索功能
  3，查看本机did，公参
  4，本机拉到的云控配置

##  Usage
```dart
void main() {
  if (!kReleaseMode) {
    _bugly(ToolsRootWidget(child: MyApp()));
    _setupApmConfig();
  } else {
    _bugly(MyApp());
  }
}

// runApp 之前调用
static void init() {
    ...
    if (!kReleaseMode) {
      TestToolsWidgetsBinding.ensureInitialized();
    } else {
      WidgetsFlutterBinding.ensureInitialized();
    }
    ...
  }

// 配置
void _setupApmConfig() async {
  final config = TestToolsConfig();
  // did
  config.onGetDid = () async {
    final did = await getDidFunction;
    return did ?? '';
  };
  // 公参
  config.onGetBaseParams = () async {
    final params = await getService<AbsHttpService>()?.getBaseParams();
    return params ?? {};
  };
  // 云控配置
  config.onGetRemoteConfig = () async {
    final config = await getService<AbsRemoteConfigService>()?.getAllConfig?.getAll();
    return config ?? {};
  };
  TestTools.setConfig(config);

  // 打点
  getService<AbsStatisticsService>()?.addInterceptor(_StatisticsInterceptor());
}

class _StatisticsInterceptor extends StatisticsInterceptor {
  @override
  Map<String, String>? baseParams() {
    return {};
  }

  @override
  void onLog(String event, Map<String, String> params) {
    TestTools.addStatisticLog(StatisticModel(
      action: event,
      params: params,
      milliseconds: DateTime.now().millisecondsSinceEpoch
    ));
  }
}
```


其他待完善
